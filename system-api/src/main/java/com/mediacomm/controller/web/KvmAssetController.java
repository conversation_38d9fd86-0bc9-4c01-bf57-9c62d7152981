package com.mediacomm.controller.web;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.message.reqeust.body.ExtendChangeBody;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.entity.vo.PageResult;
import com.mediacomm.pojo.PresetPositionDto;
import com.mediacomm.system.annotation.OperationLogRecord;
import com.mediacomm.system.base.controller.SkyLinkController;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.variable.ResUrlDef;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.RoutingOperation;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.OperateType;
import com.mediacomm.system.variable.sysenum.SubSystemType;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RpcSenderUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collection;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * .
 */
@Tag(name = "Kvm外设管理", description = "Kvm外设配置的API")
@RestController
@RequestMapping(ResUrlDef.EXTEND)
@Slf4j
public class KvmAssetController extends SkyLinkController<KvmAsset, KvmAssetService> {

  @Autowired
  RpcSenderUtils senderUtils;

  @Operation(summary = "分页查询外设信息，并支持对以主机进行匹配，以及外设名称、hardcode、主机内id模糊匹配")
  @Parameter(name = "currentPage", description = "当前页码", required = true)
  @Parameter(name = "pageSize", description = "每页行数", required = true)
  @Parameter(name = "masterId", description = "主机Id")
  @Parameter(name = "name", description = "外设名称")
  @Parameter(name = "hardcode", description = "硬件编码")
  @Parameter(name = "deviceId", description = "主机内Id")
  @GetMapping("/kvm-extend-devices/page")
  public Result<PageResult<KvmAssetVo>> getByPage(@RequestParam("currentPage") Integer currentPage,
                                                  @RequestParam("pageSize") Integer pageSize,
                                                  @RequestParam(name = "masterId", required = false)
                                                  String masterId,
                                                  @RequestParam(name = "name", required = false)
                                                  String name,
                                                  @RequestParam(name = "hardcode", required = false)
                                                  String hardcode,
                                                  @RequestParam(name = "deviceId", required = false)
                                                  Integer id) {
    return Result.ok(service.allByPage(currentPage, pageSize, masterId, name, hardcode, id));
  }

  @Operation(summary = "通过主机Id或设备型号或型号类型进行过滤")
  @GetMapping("/kvm-extend-devices")
  public Result<Collection<KvmAssetVo>> getAssetByMasterIdOrDeviceModel(@RequestParam("masterId")
                                                                        String masterId,
                                                                        @RequestParam(
                                                                                name = "deviceType",
                                                                                required = false)
                                                                        DeviceType deviceType,
                                                                        @RequestParam(
                                                                                name = "subSystemType",
                                                                                required = false)
                                                                        SubSystemType subType) {
    if (deviceType == null && subType != null) {
      return Result.ok(service.allByMasterIdAndSubSystem(masterId, subType));
    } else if (deviceType != null && subType == null) {
      return Result.ok(service.allByDeviceModelId(deviceType.getDeviceTypeId(), masterId));
    } else {
      return Result.ok(service.allByMasterId(masterId));
    }
  }

  @Operation(summary = "通过Id获取指定外设信息")
  @GetMapping("/{id}")
  public Result<KvmAssetVo> getAsset(@PathVariable String id) {
    return Result.ok(service.oneById(id));
  }

  /**
   * .
   */
  @OperationLogRecord(title = "修改指定外设", operateType = OperateType.UPDATE,
          requestBody = "#{kvmAsset}")
  @Operation(summary = "通过Id修改指定外设信息")
  @PutMapping("/{id}")
  public Result<String> updateAsset(@PathVariable String id, @RequestBody KvmAsset kvmAsset) {
    kvmAsset.setAssetId(id);
    service.updateById(kvmAsset);
    if (Objects.equals(kvmAsset.getDeviceModel(), DeviceType.CAESAR_FOUR_SCREEN_RX.getDeviceTypeId())) {
      previewAssoChangeNotice(RoutingOperation.ASSET_UPDATE, kvmAsset, DeviceType.CAESAR_FOUR_SCREEN_RX);
    }
    return Result.ok();
  }

  @Operation(summary = "修改云台设备的预案名称")
  @PostMapping("/ptz/screen")
  public Result<String> updatePtzScreenName(@RequestParam String assetId,
                                            @RequestBody Collection<PresetPositionDto> ptzScreenNames) {
    KvmAsset asset = service.getById(assetId);
    if (asset == null) {
      return Result.failure("The asset is not exist.", ResponseCode.EX_FAILURE_400);
    }
    a: for (PresetPositionDto ptzScreenName : ptzScreenNames) {
      for (Property property : asset.getCollectorProperties()) {
        if (property.getPropertyKey().equals(ptzScreenName.getPosition())) {
          property.setPropertyValue(ptzScreenName.getName());
          continue a;
        }
      }
      asset.getCollectorProperties().add(new Property(ptzScreenName.getPosition(),
              ptzScreenName.getName()));
    }
    service.updateById(asset);
    return Result.ok();
  }

  @OperationLogRecord(title = "删除指定外设", operateType = OperateType.DELETE)
  @Operation(summary = "通过Id删除指定外设信息")
  @DeleteMapping("/{id}")
  public Result<String> delAsset(@PathVariable String id) {
    KvmAssetVo kvmAsset = service.oneById(id);
    service.removeById(id);
    DeviceType deviceType = DeviceType.valueOf(kvmAsset.getDeviceType());
    if (Objects.equals(deviceType, DeviceType.CAESAR_FOUR_SCREEN_RX)
        || Objects.equals(deviceType, DeviceType.CAESAR_R2P4F)) {
      return previewAssoChangeNotice(RoutingOperation.ASSET_UPDATE, kvmAsset, deviceType);
    }
    return Result.ok();
  }

  private Result<String> previewAssoChangeNotice(String topicDto, KvmAsset asset, DeviceType deviceType) {
    if (asset != null) {
      MqRequest<ExtendChangeBody> request = new MqRequest<>();
      ExtendChangeBody body = ExtendChangeBody.builder()
              .extendId(asset.getAssetId())
              .deviceType(deviceType).build();
      request.setMasterId(asset.getMasterId());
      request.setBody(body);
      return JsonUtils.decode(senderUtils.send(asset.getMasterId(), topicDto, request),
              new TypeReference<>() {});
    } else {
      return Result.failure("No exist asset!", ResponseCode.EX_FAILURE_400);
    }
  }
}
