package com.mediacomm.system.variable;


/**
 * 消息路由键定义类.
 * 规则：topic.queue.${SERVER_NAME}
 */
public class MessageType {

  private MessageType() {
    throw new IllegalStateException("Utility class");
  }

  /**
   * web服务消息队列路由键.
   */
  public static final String SERVICE_WEB = "topic.queue.WEB_SERVER";
  /**
   * 云台服务.
   */
  public static final String PTZ_SERVER = "topic.queue.PTZ_SERVER";
  public static final String KAITO02_KVM = "topic.queue.KAITO02_KVM";
  /**
   * CaesarKvm服务消息队列路由键.
   */
  public static final String CAESAR_KVM = "topic.queue.CAESAR_KVM";
  /**
   * 监控服务消息队列路由键.
   */
  public static final String MONITOR = "topic.queue.MONITOR";
  /**
   * AirconKvm服务消息队列路由键.
   */
  public static final String AIRCON_KVM = "topic.queue.AIRCON_KVM";
  /**
   * HikvisionServer服务消息队列路由键.
   */
  public static final String HIKVISION_SERVER = "topic.queue.HIKVISION_SERVER";
  /**
   * GB Gateway服务消息队列路由键.
   */
  public static final String GB_GATEWAY_SERVER = "topic.queue.GB_GATEWAY_SERVER";
  /**
   * TsingliServer服务消息队列路由键.
   */
  public static final String TSINGLI_SERVER = "topic.queue.TSINGLI_SERVER";
  /**
   * KVM SWITCHER服务消息队列路由键.
   */
  public static final String KVM_SWITCHER = "topic.queue.KVM_SWITCHER";

}
