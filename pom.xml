<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.1.4</version>
    <relativePath/> <!-- lookup parent from repository -->
  </parent>
  <groupId>com.mediacomm</groupId>
  <artifactId>skylink-server</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <packaging>pom</packaging>
  <name>skylink-server</name>
  <modules>
    <module>base-core</module>
    <module>system-api</module>
    <module>kvm-collectors</module>
    <module>system-monitor</module>
    <module>system-protocol</module>
  </modules>
  <properties>
    <java.version>21</java.version>
    <camel.version>3.20.4</camel.version>
    <mybatis-plus.version>3.5.3.1</mybatis-plus.version>
    <mysql.version>8.0.33</mysql.version>
    <snakeyaml.version>2.0</snakeyaml.version>
    <jackson.version>2.15.0</jackson.version>
    <springdoc.version>2.1.0</springdoc.version>
    <spring-cloud.version>2022.0.2</spring-cloud.version>
    <spring-cloud.alibaba.version>2022.0.0.0-RC2</spring-cloud.alibaba.version>
    <artifact.version>${project.version}</artifact.version>
    <dependency.check.version>8.3.1</dependency.check.version>
    <maven.checkstyle.version>3.2.2</maven.checkstyle.version>
    <maven.surefire.version>3.2.5</maven.surefire.version>
    <maven.jacoco.version>0.8.12</maven.jacoco.version>
    <maven.pmd.version>3.23.0</maven.pmd.version>
    <maven.spotbugs.version>4.7.3.4</maven.spotbugs.version>
    <druid.version>1.2.15</druid.version>
    <jwt.version>9.31</jwt.version>
    <mapstruct.version>1.5.5.Final</mapstruct.version>
    <shedlock.version>5.6.0</shedlock.version>
    <dockerfile-maven.version>1.4.13</dockerfile-maven.version>
    <hutool.version>5.8.25</hutool.version>
    <poi.version>4.1.2</poi.version>
    <compress.version>1.26.2</compress.version>
    <snmp4j.version>3.7.7</snmp4j.version>
    <httpclient.version>4.5.14</httpclient.version>
    <protcol-buffer.version>4.26.1</protcol-buffer.version>
    <datafaker.version>2.3.1</datafaker.version>
    <lombok.version>1.18.34</lombok.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <!-- 解决springboot中依赖的snakeyaml版本太低导致的漏洞警告 -->
      <dependency>
        <groupId>org.yaml</groupId>
        <artifactId>snakeyaml</artifactId>
        <version>${snakeyaml.version}</version>
      </dependency>
      <!-- poi不能升到5版本，否则会跟hutool不兼容，因此单独升级commons-compress的版本，来解决漏洞警告 -->
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-compress</artifactId>
        <version>${compress.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-dependencies</artifactId>
        <version>${spring-cloud.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-alibaba-dependencies</artifactId>
        <version>${spring-cloud.alibaba.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
      <exclusions>
        <!-- fix:Standard Commons Logging discovery in action with spring-jcl: please remove commons-logging.jar from classpath in order to avoid potential conflicts -->
        <exclusion>
          <groupId>commons-logging</groupId>
          <artifactId>commons-logging</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <!-- fix:No spring.config.import property has been defined -->
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-bootstrap</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-openfeign</artifactId>
      <exclusions>
        <exclusion>
          <groupId>commons-fileupload</groupId>
          <artifactId>commons-fileupload</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>${lombok.version}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-amqp</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-devtools</artifactId>
      <scope>runtime</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.integration</groupId>
      <artifactId>spring-integration-mqtt</artifactId>
    </dependency>
<!--    <dependency>-->
<!--      <groupId>org.springframework.boot</groupId>-->
<!--      <artifactId>spring-boot-docker-compose</artifactId>-->
<!--      <optional>true</optional>-->
<!--    </dependency>-->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <!--https://stackoverflow.com/questions/6178583/maven-does-not-find-junit-tests-to-run-->
    <dependency>
      <groupId>org.junit.vintage</groupId>
      <artifactId>junit-vintage-engine</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
      <version>${mapstruct.version}</version>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct-processor</artifactId>
      <version>${mapstruct.version}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.mapstruct.extensions.spring</groupId>
      <artifactId>mapstruct-spring-annotations</artifactId>
      <version>1.0.1</version>
    </dependency>
    <dependency>
      <groupId>net.javacrumbs.shedlock</groupId>
      <artifactId>shedlock-spring</artifactId>
      <version>${shedlock.version}</version>
    </dependency>
    <dependency>
      <groupId>net.javacrumbs.shedlock</groupId>
      <artifactId>shedlock-provider-redis-spring</artifactId>
      <version>${shedlock.version}</version>
    </dependency>
    <dependency>
      <groupId>net.datafaker</groupId>
      <artifactId>datafaker</artifactId>
      <version>${datafaker.version}</version>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.11.0</version>
        <configuration>
          <source>21</source>
          <target>21</target>
          <annotationProcessorPaths>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${lombok.version}</version>
            </path>
            <path>
              <groupId>org.mapstruct</groupId>
              <artifactId>mapstruct-processor</artifactId>
              <version>${mapstruct.version}</version>
            </path>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok-mapstruct-binding</artifactId>
              <version>0.2.0</version>
            </path>
            <path>
              <groupId>org.springframework.boot</groupId>
              <artifactId>spring-boot-configuration-processor</artifactId>
              <version>3.1.4</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
      <!--<plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>${maven.checkstyle.version}</version>
        <dependencies>
          <dependency>
            <groupId>com.puppycrawl.tools</groupId>
            <artifactId>checkstyle</artifactId>
            <version>10.12.1</version>
          </dependency>
        </dependencies>
        <configuration>
          <configLocation>google_checks.xml</configLocation>
          <failsOnError>true</failsOnError>
          <failOnViolation>true</failOnViolation>
          <violationSeverity>info</violationSeverity>
        </configuration>
        <executions>
          <execution>
            <id>checkstyle</id>
            <phase>validate</phase>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <failOnViolation>true</failOnViolation>
            </configuration>
          </execution>
        </executions>
      </plugin>-->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>${maven.pmd.version}</version>
        <configuration>
          <analysisCache>true</analysisCache>
          <linkXRef>true</linkXRef>
        </configuration>
        <executions>
          <execution>
            <id>pmd</id>
            <phase>validate</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
        <version>${maven.spotbugs.version}</version>
        <configuration>
          <xmlOutput>true</xmlOutput>
          <excludeFilterFile>spotbugs-exclude.xml</excludeFilterFile>
        </configuration>
        <executions>
          <execution>
            <id>spotbugs</id>
            <phase>compile</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <!--<plugin>
        <groupId>org.owasp</groupId>
        <artifactId>dependency-check-maven</artifactId>
        <version>${dependency.check.version}</version>
        <configuration>
          <suppressionFiles>
            <suppressionFile>owasp-suppressions.xml</suppressionFile>
          </suppressionFiles>
          <autoUpdate>true</autoUpdate>
          <failBuildOnCVSS>7</failBuildOnCVSS>
        </configuration>
        <executions>
          <execution>
            <id>dependency-check</id>
            <phase>validate</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>-->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${maven.surefire.version}</version>
        <configuration>
          <properties>
            <property>
              <name>junit</name>
              <value>true</value>
            </property>
          </properties>
          <includes>
            <include>**/*Test.java</include>
          </includes>
        </configuration>
      </plugin>
      <!--覆盖率报告-->
    </plugins>
  </build>
</project>
