# 智能设备管理系统架构设计文档

## 1. 系统概述

```
本系统为智能设备集中管理平台，支持多协议设备接入、实时状态监控、操作日志记录、消息队列分发等功能。系统采用微服务架构，核心模块包含设备管理、权限认证、消息代理、协议适配等子系统。
```

## 2. 架构全景图（Mermaid）

```mermaid
flowchart TD
    subgraph 接入层
        A[HTTPS/WebSocket] --> B[API Gateway]
        C[设备协议] --> D[Protocol Adapter]
    end
  
    subgraph 业务层
        B --> E[Auth Service]
        B --> F[Device Service]
        D --> F
        F --> G[Message Broker]
    end
  
    subgraph 基础设施
        E --> H[(Redis Auth)]
        F --> I[(PostgreSQL)]
        G --> J[(RabbitMQ)]
        K[Prometheus] --> L[Grafana]
    end
  
    subgraph 设备层
        M[KVM设备] --> D
        N[PTZ设备] --> D
        O[温控设备] --> D
    end
```

## 3. 核心模块设计

### 3.1 设备管理服务

```mermaid
classDiagram
    class DeviceController {
        +GetStatus(deviceID)
        +SendCommand(cmd)
        +BatchUpdate()
    }
  
    class DeviceService {
        +ValidateCommand()
        +CreateOperationLog()
        +PublishMessage()
    }
  
    class ProtocolAdapter {
        +ParseModbus()
        +ParseSNMP()
        +GenerateHL7()
    }
  
    DeviceController --> DeviceService
    DeviceService --> ProtocolAdapter
```

### 3.2 消息队列设计

| 队列名称        | 路由键格式        | TTL | 死信处理      | 用途描述         |
| --------------- | ----------------- | --- | ------------- | ---------------- |
| device.commands | device.{type}.cmd | 30s | 重试3次后归档 | 设备控制指令队列 |
| status.updates  | status.#          | -   | 直接丢弃      | 状态更新广播队列 |
| alarm.events    | alarm.{level}     | 60s | 转人工处理    | 告警事件处理队列 |

## 4. 技术选型矩阵

### 4.1 基础组件

| 组件类型 | Java方案            | Golang替代方案 | 迁移优势           |
| -------- | ------------------- | -------------- | ------------------ |
| Web框架  | Spring MVC          | Gin            | 路由性能提升40%    |
| ORM      | MyBatis Plus        | GORM           | 开发效率提升35%    |
| 配置中心 | Spring Cloud Config | Viper          | 内存占用降低60%    |
| 消息队列 | RabbitMQ            | RabbitMQ+amqp  | 协议兼容，无缝迁移 |

### 4.2 性能关键组件对比

```
barChart
    title 性能对比（QPS）
    category Java,Golang
    Series 认证服务 1200, 3200
    Series 消息处理 850, 2200
    Series 协议解析 650, 1800
```

## 5. 数据存储设计

### 5.1 核心表结构（示例）

```sql
CREATE TABLE kvm_operation_logs (
    id BIGSERIAL PRIMARY KEY,
    device_id VARCHAR(36) NOT NULL,
    operation_type SMALLINT NOT NULL CHECK(operation_type BETWEEN 0 AND 5),
    operator VARCHAR(64) NOT NULL,
    command JSONB NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    INDEX idx_device_ops (device_id, operation_type)
);

CREATE TABLE device_status (
    device_id VARCHAR(36) PRIMARY KEY,
    last_heartbeat TIMESTAMPTZ NOT NULL,
    cpu_usage NUMERIC(5,2),
    mem_usage NUMERIC(5,2),
    connections SMALLINT DEFAULT 0
) PARTITION BY RANGE (last_heartbeat);
```

## 6. 接口规范

### 6.1 设备状态查询接口

```yaml
/device/{id}/status:
  get:
    tags:
      - Device
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
    responses:
      200:
        content:
          application/json:
            schema:
              type: object
              properties:
                cpuUsage:
                  type: number
                  format: float
                memUsage: 
                  type: number
                  format: float
                lastUpdate:
                  type: string
                  format: date-time
```

## 7. 非功能性需求

1. **性能指标**：

   - API响应时间 ≤200ms（P99）
   - 消息处理吞吐 ≥5000 msg/s
   - 支持500+并发设备连接
2. **可靠性**：

   - 核心服务99.95%可用性
   - 数据持久化率100%
   - 故障恢复时间 ≤5分钟
3. **安全要求**：

   - 全链路HTTPS加密
   - JWT令牌有效期≤4小时
   - 敏感操作二次认证

## 8. 部署架构

```mermaid
graph TB
    subgraph 生产集群
        A[HAProxy LB] --> B[API Gateway]
        B --> C[Service Pods]
        C --> D[(PG Cluster)]
        C --> E[(Redis Cluster)]
        F[RabbitMQ Cluster] --> C
        G[Monitor] --> H[Prometheus]
        H --> I[AlertManager]
    end
  
    subgraph 协议区
        J[Protocol Adapters] --> K[物理设备]
        J --> L[虚拟设备]
    end
```

## 9. 迁移路线图

```gantt
    title 系统迁移里程碑
    dateFormat  YYYY-MM-DD
    section 基础架构
    容器平台搭建     :2025-06-01, 30d
    监控体系迁移     :2025-06-15, 20d
    section 服务迁移
    认证服务迁移     :2025-07-01, 15d
    设备服务迁移     :2025-07-10, 25d
    消息队列迁移     :2025-07-20, 10d
    section 验证阶段
    压力测试       :2025-08-01, 10d
    灰度发布       :2025-08-10, 14d
```
