#################### MySQL #####################
MYSQL_VERSION=8.0.19-12
MYSQL_HOST_PORT=3306
MYSQL_USER_NAME=root
MYSQL_ROOT_PASSWORD=123456
#此处要主要的是要写mysql服务的host，不能写127.0.0.1；直接写mysql即可
MYSQL_SERVICE_HOST=mysql

#################### Kingbase #####################
KINGBASE_VERSION=v1-1
KINGBASE_HOST_PORT=54321
KINGBASE_USER_NAME=skylink
KINGBASE_SKYLINK_PASSWORD=123456

#################### Redis #####################
REDIS_VERSION=7.2.0-v6
REDIS_HOST_PORT=6379

#################### RabbitMQ #####################
RABBITMQ_VERSION=3.12.2-management
RABBITMQ_HOST_PORT=5672
RABBITMQ_MAG_PORT=15672
RABBITMQ_MQTT_PORT=1883
RABBITMQ_DEFAULT_USER=admin
RABBITMQ_DEFAULT_PASS=123456

#################### NACOS #####################
NACOS_VERSION=v2.2.2-slim
PREFER_HOST_MODE=hostname
MODE=standalone
SPRING_DATASOURCE_PLATFORM=mysql
MYSQL_SERVICE_DB_NAME=nacos_config

#################### NGINX #####################
NGINX_VERSION=latest
NGINX_PROXY_FILE=/mydata/nginx/proxy.conf
NGINX_CONF_FILE=/mydata/nginx/nginx.conf
NGINX_HTML_DIR=/mydata/nginx/html

#################### skylink-server-application #####################
MYSQL_SKYLINK_DB_NAME=skylink

#################### SHARE #####################
DATABASE_DRIVER_CLASS=com.mysql.cj.jdbc.Driver
#DATABASE_DRIVER_CLASS=com.kingbase8.Driver
DATABASE_USER=root
#DATABASE_USER=skylink
DATABASE_PASSWORD=123456
DATABASE_HOST=mysql
#DATABASE_HOST=kingbase
DATABASE_DRIVER_NAME=mysql
#DATABASE_DRIVER_NAME=kingbase8
DATABASE_JDBC_URL_PARAMS=serverTimezone=Asia/Shanghai&createDatabaseIfNotExist=true
#DATABASE_JDBC_URL_PARAMS=stringtype=unspecified
DATABASE_PORT=3306
#DATABASE_PORT=54321
DATABASE_NAME=skylink

REDIS_SERVICE_HOST=redis
RABBITMQ_SERVICE_HOST=rabbitmq
NACOS_SERVICE_HOST=nacos
NACOS_SERVICE_PORT=8848
