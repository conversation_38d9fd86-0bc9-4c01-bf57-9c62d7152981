package com.mediacomm.caesar.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mediacomm.caesar.domain.CaesarConstants;
import com.mediacomm.caesar.domain.CaesarDeviceNetInfo;
import com.mediacomm.caesar.domain.CaesarDeviceStatus;
import com.mediacomm.caesar.domain.CaesarMqRequest;
import com.mediacomm.caesar.domain.CaesarPanelRect;
import com.mediacomm.caesar.domain.CaesarPreviewStrategyMode;
import com.mediacomm.caesar.domain.CaesarRedundantMode;
import com.mediacomm.caesar.domain.CaesarResponse;
import com.mediacomm.caesar.domain.CaesarRx;
import com.mediacomm.caesar.domain.CaesarSeat;
import com.mediacomm.caesar.domain.CaesarSeatPanelRect;
import com.mediacomm.caesar.domain.CaesarSeatPanels;
import com.mediacomm.caesar.domain.CaesarServer;
import com.mediacomm.caesar.domain.CaesarSwitchBanner;
import com.mediacomm.caesar.domain.CaesarSwitchBannerColor;
import com.mediacomm.caesar.domain.CaesarSwitchBottomImage;
import com.mediacomm.caesar.domain.CaesarTx;
import com.mediacomm.caesar.domain.CaesarUser;
import com.mediacomm.caesar.domain.CaesarVideoPanels;
import com.mediacomm.caesar.domain.CaesarVideoWall;
import com.mediacomm.caesar.domain.CaesarVwScene;
import com.mediacomm.caesar.domain.ConnectStatus;
import com.mediacomm.caesar.domain.EncoderDetailParam;
import com.mediacomm.caesar.domain.EncoderEdidParam;
import com.mediacomm.caesar.domain.KvmOperationFrom;
import com.mediacomm.caesar.domain.RedundantModeEnum;
import com.mediacomm.caesar.domain.avgm.TxConnectRequest;
import com.mediacomm.caesar.domain.kaito.Action;
import com.mediacomm.caesar.domain.kaito.KaitoGroupData;
import com.mediacomm.caesar.domain.kaito.KaitoInputData;
import com.mediacomm.caesar.domain.kaito.KaitoIpcData;
import com.mediacomm.caesar.domain.kaito.KaitoPanelData;
import com.mediacomm.caesar.domain.kaito.KaitoPanelsData;
import com.mediacomm.caesar.domain.kaito.KaitoScreenDetail;
import com.mediacomm.caesar.domain.kaito.KaitoVideoServerInfo;
import com.mediacomm.caesar.domain.kaito.KaitoVideoWall;
import com.mediacomm.caesar.domain.kaito.KaitoVideoWallGroup;
import com.mediacomm.caesar.domain.kaito.KaitoWriteSourceReq;
import com.mediacomm.caesar.preview.PreviewManager;
import com.mediacomm.caesar.util.InspectCaesarUtil;
import com.mediacomm.caesar.util.InspectCaesarUtil.VideoResolutionTypeEnum;
import com.mediacomm.caesar.util.mapper.CaesarEntityMapper;
import com.mediacomm.caesar.util.mapper.jpa.KvmAssetContext;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmSeat;
import com.mediacomm.entity.dao.KvmSlot;
import com.mediacomm.entity.dao.KvmUser;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.entity.dao.VideoWallBanner;
import com.mediacomm.entity.dto.OptionDto;
import com.mediacomm.entity.message.LayerData;
import com.mediacomm.entity.message.LayoutData;
import com.mediacomm.entity.message.PanelRect;
import com.mediacomm.entity.message.SeatPanels;
import com.mediacomm.entity.message.VideoPanels;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.entity.message.reqeust.body.ChannelIdRequestBody;
import com.mediacomm.entity.message.reqeust.body.ConnectTxRxRequestBody;
import com.mediacomm.entity.message.reqeust.body.DecoderIdRequestBody;
import com.mediacomm.entity.message.reqeust.body.ExtendChangeBody;
import com.mediacomm.entity.message.reqeust.body.Heartbeat;
import com.mediacomm.entity.message.reqeust.body.IdsRequestBody;
import com.mediacomm.entity.message.reqeust.body.ImageRequestBody;
import com.mediacomm.entity.message.reqeust.body.ObjectId;
import com.mediacomm.entity.message.reqeust.body.ObjectIds;
import com.mediacomm.entity.message.reqeust.body.OpenVwPanelsRequestBody;
import com.mediacomm.entity.message.reqeust.body.PanelRectRequestBody;
import com.mediacomm.entity.message.reqeust.body.PeripheralUpgradeIdRequestBody;
import com.mediacomm.entity.message.reqeust.body.PeripheralUpgradeTaskBody;
import com.mediacomm.entity.message.reqeust.body.SeatOpenTxRequestBody;
import com.mediacomm.entity.message.reqeust.body.SeatOpenTxesRequestBody;
import com.mediacomm.entity.message.reqeust.body.SnapshotStatus;
import com.mediacomm.entity.message.reqeust.body.SwapVwPanelLayerRequestBody;
import com.mediacomm.entity.message.reqeust.body.TxIdRequestBody;
import com.mediacomm.entity.message.reqeust.body.VideoWallChangeBody;
import com.mediacomm.entity.message.reqeust.body.VwBannerRequestBody;
import com.mediacomm.entity.message.reqeust.body.VwEnableRequestBody;
import com.mediacomm.entity.vo.EncoderAssoVo;
import com.mediacomm.entity.vo.KvmAssetVo;
import com.mediacomm.system.base.kvm.CmdServer;
import com.mediacomm.system.service.VideoWallBannerService;
import com.mediacomm.system.variable.PropertyKeyConst;
import com.mediacomm.system.variable.RedisKey;
import com.mediacomm.system.variable.ResponseCode;
import com.mediacomm.system.variable.sysenum.BannerType;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.system.variable.sysenum.LayoutType;
import com.mediacomm.system.variable.sysenum.SnapshotType;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RedisUtil;
import feign.FeignException;
import jakarta.annotation.Resource;
import java.net.URI;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;

/**
 * CaesarCmdServer.
 */
@Slf4j
@Controller
public class CaesarCmdServer extends CmdServer {

  private static final int PORT = 8080;
  @Resource
  @Getter
  private CaesarFeignClientApi cli;
  @Resource
  private RedisUtil redisUtil;
  @Resource
  private PreviewManager previewManager;
  @Resource
  private CaesarEntityMapper caesarEntityMapper;
  @Resource
  private CaesarKaitoCmd caesarKaitoCmd;
  @Resource
  private Vp7CmdServer vp7CmdServer;
  @Resource
  private VideoWallBannerService bannerService;

  /**
   * caesar kvm reboot host or ext.
   */
  public String reboot(String msg) {
    MqRequest request = JsonUtils.decode(msg, MqRequest.class);
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    URI uri = URI.create(String.format(URI_FORMAT, kvmMaster.getDeviceIp(), PORT));
    cli.rebootServer(uri, kvmMaster.getDeviceIp());
    return Result.okStr();
  }

  /**
   * refreshExtendDevice.
   */
  public String refreshExtendDevice(String msg) {
    MqRequest request = JsonUtils.decode(msg, MqRequest.class);
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }

    return JsonUtils.encode("");
  }

  /**
   * refreshConfig.
   */
  public String refreshConfig(String msg) {
    MqRequest<Void> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    String masterId = request.getMasterId();
    KvmMaster kvmMaster = kvmMasterService.getById(masterId);
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    return refreshConfig(kvmMaster);
  }

  /**
   * refreshConfig.
   */
  private String refreshConfig(KvmMaster kvmMaster) {
    URI uri = URI.create(String.format(URI_FORMAT, kvmMaster.getDeviceIp(), PORT));
    List<CaesarTx> txList = cli.getTxList(uri);
    Collection<String> txHardcodeList = txList.stream().map(tx -> InspectCaesarUtil
            .getCaesarDeviceHardCode(kvmMaster.getMasterId(), tx.getSn(), tx.getId())).toList();
    kvmAssetService.delBatchNotExistByHardcode(txHardcodeList, kvmMaster.getMasterId(), DeviceType.CAESAR_TX);
    for (CaesarTx caesarTx : txList) {
      if (caesarTx.getLink1Port() != 0 || caesarTx.getLink2Port() != 0) {
        KvmAsset kvmAsset = caesarEntityMapper.toKvmAsset(caesarTx, kvmMaster.getMasterId());
        kvmAssetService.saveOrUpdateKvmAsset(kvmAsset, kvmAsset.getMasterId());
      }
    }

    List<CaesarRx> rxList = cli.getRxList(uri);
    Collection<String> rxHardcodeList = rxList.stream().map(rx -> InspectCaesarUtil
            .getCaesarDeviceHardCode(kvmMaster.getMasterId(), rx.getSn(), rx.getId())).collect(Collectors.toSet());
    kvmAssetService.delBatchNotExistByHardcode(rxHardcodeList, kvmMaster.getMasterId(), DeviceType.CAESAR_RX,
            DeviceType.CAESAR_FOUR_SCREEN_RX, DeviceType.CAESAR_KAITO_SECOND_CARD, DeviceType.CAESAR_DECODE_DEVICE,
            DeviceType.CAESAR_VP7, DeviceType.CAESAR_R2P4F);
    for (CaesarRx caesarRx : rxList) {
      if (caesarRx.getLink1Port() != 0 || caesarRx.getLink2Port() != 0) {
        KvmAsset kvmAsset = caesarEntityMapper.toKvmAsset(caesarRx, kvmMaster.getMasterId());
        if (Objects.equals(kvmAsset.getDeviceModel(), DeviceType.CAESAR_VP7.getDeviceTypeId())
                || Objects.equals(kvmAsset.getDeviceModel(), DeviceType.CAESAR_R2P4F.getDeviceTypeId())) { // vp7 || r2p4f
          List<CaesarDeviceNetInfo> info = cli.getDeviceNetInfo(uri, caesarRx.getId()).stream().toList();
          if (!info.isEmpty()) {
            kvmAsset.setDeviceIp(info.getFirst().getIp());
          } else {
            log.warn("Unknown VP7 or R2P4F device {} ip.", kvmAsset.getName());
          }
        }
        kvmAssetService.saveOrUpdateKvmAsset(kvmAsset, kvmAsset.getMasterId());
        if (Objects.equals(kvmAsset.getDeviceModel(), DeviceType.CAESAR_R2P4F.getDeviceTypeId())
                && StringUtils.isNotEmpty(kvmAsset.getDeviceIp())) {
          // 使用r2p4f做预览
          previewManager.updatePreviewStatus(DeviceType.CAESAR_R2P4F, kvmMaster.getMasterId());
        }
      }
    }

    List<CaesarVideoWall> videoWalls = cli.getVideoWalls(uri);
    Map<String, String> caesarWallMap = videoWalls.stream().collect(
            Collectors.toMap(item -> String.valueOf(item.getId()), JsonUtils::encode));
    // 缓存凯撒接口大屏数据
    redisUtil.del(RedisKey.KC_SC + kvmMaster.getMasterId());
    redisUtil.hset(RedisKey.KC_SC + kvmMaster.getMasterId(), caesarWallMap);
    List<KvmVideoWall> kvmVideoWallList =
        caesarEntityMapper.toKvmVideoWallList(videoWalls, kvmMaster.getMasterId());
    kvmVideoWallList.forEach(item -> {
      if (item.getDeviceId() != -1) {
        videoWallService.saveOrUpdateByUniqueSearchKey(item);
      }
    });

    List<CaesarSeat> seatList = cli.getSeatList(uri);
    List<KvmSeat> kvmSeatList = caesarEntityMapper.toKvmSeatList(seatList, kvmMaster.getMasterId());
    kvmSeatList.forEach(item -> {
      if (StringUtils.isNotEmpty(item.getName()) && !item.getDecoders().isEmpty()) {
        item.getDecoders().forEach(decoder -> {
          KvmAssetVo rx =
              kvmAssetService.oneByDeviceId(decoder.getDeviceId(), kvmMaster.getMasterId());
          if (rx != null) {
            decoder.setAssetId(rx.getAssetId());
            decoder.setDeviceName(rx.getName());
            decoder.setDeviceType(rx.getDeviceType());
          }
        });
        kvmSeatService.saveOrUpdate(item, kvmMaster.getMasterId());
      }
    });

    List<CaesarServer> serverInfo = cli.getServerInfo(uri);
    for (CaesarServer caesarServer : serverInfo) {
      KvmMaster kvmMasterNew = caesarEntityMapper.toKvmMaster(caesarServer);
      kvmMaster.setAlias(kvmMasterNew.getName());
      kvmMaster.setVersion(kvmMasterNew.getVersion());
      kvmMaster.setProperties(kvmMasterNew.getProperties());
      kvmMasterService.updateById(kvmMaster);
      List<KvmSlot> kvmSlots = caesarServer.getSlotInfos().stream().map(
          caesarSlotInfo -> caesarEntityMapper
              .toKvmSlot(caesarSlotInfo, kvmMaster.getMasterId())).toList();
      kvmSlotService.saveOrUpdateBatchByDeviceIdAndMasterId(kvmSlots);
    }
    List<CaesarUser> users = cli.getUsers(uri);
    List<KvmUser> kvmUserList = caesarEntityMapper.toKvmUserList(users, kvmMaster.getMasterId());
    kvmUserService.saveOrUpdate(kvmUserList, kvmMaster.getMasterId());

    // 关联嗨动
    DeviceType extendDeviceType =
        InspectCaesarUtil.getDeviceTypeFromCollectorProperties(kvmMaster.getCollectorProperties());
    if (extendDeviceType == DeviceType.KAITO02) {
      try {
        // 嗨动拼接屏
        Collection<KaitoVideoWallGroup> kaitoVideoWallGroups = cli.getKaitoVideoWallGroups(uri);
        for (KaitoVideoWallGroup kaitoVideoWallGroup : kaitoVideoWallGroups) {
          KaitoGroupData group = cli.getKaitoVideoWallGroup(uri, kaitoVideoWallGroup.getId());
          kaitoVideoWallGroup.getVideowalls().forEach(videoWall -> {
            KaitoVideoWall kaitoVideoWall =
                cli.getKaitoVideoWall(uri, kaitoVideoWallGroup.getId(), videoWall.getId());
            KvmVideoWall kvmVideoWall =
                caesarEntityMapper.toKvmVideoWall(kaitoVideoWall, group, kvmMaster.getMasterId());
            videoWallService.saveOrUpdateByUniqueSearchKey(kvmVideoWall);
          });
          Property property = new Property("groupId", String.valueOf(kaitoVideoWallGroup.getId()));
          // 嗨动视频源输入卡
          Collection<KaitoInputData> kaitoInputData =
              cli.getKaitoInputCardList(uri, kaitoVideoWallGroup.getId());
          kaitoInputData.forEach(input -> {
            // 非子母卡
            if (input.getType() != 1) {
              KvmAsset kvmAsset = caesarEntityMapper.toKvmAsset(input, kvmMaster.getMasterId(),
                  kaitoVideoWallGroup.getId());
              kvmAsset.getProperties().add(property);
              kvmAssetService.saveOrUpdateKvmAsset(kvmAsset, kvmAsset.getMasterId());
            }
          });
          // 嗨动IPC
          Collection<KaitoIpcData> kaitoIpcData =
              cli.getKaitoIpcList(uri, kaitoVideoWallGroup.getId());
          kaitoIpcData.forEach(ipc -> {
            KvmAsset kvmAsset = caesarEntityMapper.toKvmAsset(ipc, kvmMaster.getMasterId(),
                kaitoVideoWallGroup.getId());
            kvmAsset.getProperties().add(property);
            kvmAssetService.saveOrUpdateKvmAsset(kvmAsset, kvmAsset.getMasterId());
          });
        }
      } catch (FeignException e) {
        return doFeignException(e);
      }
    }
    return Result.okStr();
  }

  /**
   * getTxSnapshot.
   */
  public String getTxSnapshot(String msg) {
    MqRequest<ObjectIds> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    RedundantModeEnum mode = getRedundantMode(kvmMaster.getMasterId());
    String relatedId = InspectCaesarUtil.getRelatedMasterId(kvmMaster);
    boolean isBackStatus = !relatedId.equals(kvmMaster.getMasterId()) && mode == RedundantModeEnum.BACKUP;
    if (isBackStatus) {
      KvmMaster relatedMaster = kvmMasterService.getById(relatedId);
      if (relatedMaster != null) {
        kvmMaster = relatedMaster;
      } else {
        return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
      }
    }

    List<SnapshotStatus> statuses = Lists.newArrayList();
    List<String> ids = request.getBody().getIds();
    List<String> activeIds = request.getBody().getActiveIds();
    ListIterator<String> iterator = ids.listIterator();
    List<String> backIds = Lists.newArrayList();
    KaitoVideoServerInfo info = null;
    int groupId = 0;
    URI uri = URI.create(String.format(URI_FORMAT, kvmMaster.getDeviceIp(), PORT));
    final String masterId = kvmMaster.getMasterId();
    while (iterator.hasNext()) {
      String id = iterator.next();
      KvmAsset tx = kvmAssetService.getById(id);
      if (isBackStatus) {
        tx = kvmAssetService.oneByDeviceId(tx.getDeviceId(), kvmMaster.getMasterId());
      }
      SnapshotStatus status;
      if (tx != null) {
        final String assetMasterId = tx.getMasterId();
        // 4k60Hz无法被预览VP6解码
        VideoResolutionTypeEnum videoResolutionType = InspectCaesarUtil.getVideoResolutionType(tx);
        CaesarPreviewStrategyMode strategyMode =
            redisUtil.getStr(RedisUtil.redisKey(RedisKey.KC_SC_SM, masterId))
                .map(CaesarPreviewStrategyMode::valueOf).orElse(CaesarPreviewStrategyMode.NONE);
        if (videoResolutionType == VideoResolutionTypeEnum.RESOLUTION_4K_60HZ
            && !InspectCaesarUtil.isSupportHighResolution(strategyMode)) {
          iterator.remove();
          activeIds.removeIf(txId -> txId.equals(id)
              || !assetMasterId.equals(masterId));
          log.warn(String.format("CaesarTx %s %s is 4K60Hz and current preview strategy is %s,"
                  + "it cannot participate in the preview.", tx.getName(), tx.getAssetId(),
              strategyMode));
          continue;
        }
        String path = String.format("/caesar/%s.jpg", tx.getHardcode());
        SnapshotType type = SnapshotType.SKYLINK;
        if (Objects.equals(tx.getDeviceModel(), DeviceType.KAITO02_INPUT.getDeviceTypeId())) {
          int kaitoDeviceGroup = Integer.parseInt(
              Property.findValueByKey(tx.getProperties(), "groupId", "0"));
          // 不是同一个嗨动主机上的input需要重新读取
          if (groupId != kaitoDeviceGroup) {
            groupId = kaitoDeviceGroup;
            info = null;
          }
          if (info == null) {
            KaitoGroupData groupData = cli.getKaitoVideoWallGroup(uri, groupId);
            URI kaitoUrl =
                URI.create(String.format(URI_FORMAT, groupData.getIp(), groupData.getPort()));
            info = caesarKaitoCmd.getVideoServerInfo(kaitoUrl, groupData);
            if (info == null) {
              return Result.failureStr("Kaito getSnapshot failed!", ResponseCode.EX_FAILURE_500);
            }
          }
          path = String.format("kaito?mvr=%s&config=%s&id=%d", info.getMvrUrl(), info.getConfigUrl(),
              tx.getDeviceId());
          type = SnapshotType.KAITO02;
        } else if (tx.getDeviceModel().equals(DeviceType.KAITO02_IPC.getDeviceTypeId())) {
          String streamType = Property.findValueByKey(
                  tx.getCollectorProperties(), PropertyKeyConst.STREAM_TYPE, "mainStreamId");
          if ("subStreamId".equals(streamType)) {
            path = Property.findValueByKey(tx.getProperties(), "subStreamUrl", "");
          } else {
            path = Property.findValueByKey(tx.getProperties(), "mainStreamUrl", "");
          }
          type = SnapshotType.RTSP;
        }
        if (type != SnapshotType.SKYLINK) {
          iterator.remove();
          activeIds.removeIf(txId -> txId.equals(id)
              || !assetMasterId.equals(masterId));
        } else {
          backIds.add(tx.getAssetId());
        }
        status = SnapshotStatus.builder().id(id).path(path).type(type).masterId(request.getMasterId())
            .linkStatus(true).build();
      } else {
        status = SnapshotStatus.builder().id(id).type(SnapshotType.SKYLINK).linkStatus(false).build();
      }
      statuses.add(status);
    }
    request.getBody().setIds(backIds);
    request.getBody().setActiveIds(backIds);
    previewManager.addPreview(masterId, request.getBody());
    return Result.okStr(statuses);
  }

  /**
   * getVmScene.
   */
  @Deprecated
  public String getVwScene(String msg) {
    MqRequest<ObjectId> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    URI uri = URI.create(String.format(URI_FORMAT, getVirtualAddress(kvmMaster), PORT));
    int caesarVideoWallId = getVideoWallDeviceIdByWallId(request.getBody().getId());
    if (caesarVideoWallId < 0) {
      return Result.failureStr(ID_FORMAT_ERROR, ResponseCode.EX_FAILURE_400);
    }
    List<CaesarVwScene> videoWallScenes = cli.getVideoWallScenes(uri, caesarVideoWallId);
    System.out.println(videoWallScenes);
    // todo return videoWallScenes

    return Result.failureStr(ID_FORMAT_ERROR, ResponseCode.EX_FAILURE_400);
  }

  /**
   * getVmPanels.
   */
  public String getVwPanels(String msg) {
    MqRequest<ObjectId> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    URI uri = URI.create(String.format(URI_FORMAT, getVirtualAddress(kvmMaster), PORT));
    KvmVideoWall wall = videoWallService.getById(request.getBody().getId());
    // 全光拼接屏
    if (Objects.equals(wall.getDeviceModel(),
        DeviceType.CAESAR_KAITO_VIDEO_WALL.getDeviceTypeId())) {
      int groupId = Integer.parseInt(Property.findValueByKey(wall.getProperties(), "groupId", "0"));
      return Result.okStr(getKaitoVideoVideoPanels(wall, uri, groupId));
    } else { // 凯撒VP6 VP7视频墙
      CaesarVideoPanels videoWallPanels = cli.getVideoWallPanels(uri, wall.getDeviceId());
      return Result.okStr(caesarEntityMapper.toVideoPanels(videoWallPanels, request.getMasterId()));
    }
  }

  /**
   * openVwPanel.
   */
  public String openVwPanel(String msg) {
    CaesarMqRequest<PanelRectRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    PanelRect panel = request.getBody().getPanelRect();
    KvmAsset tx = kvmAssetService.getById(panel.getVideoSrcId());
    KvmVideoWall videoWall = videoWallService.getById(request.getBody().getId());
    if (videoWall.getDeviceId() < 0) {
      return Result.failureStr(ID_FORMAT_ERROR, ResponseCode.EX_FAILURE_400);
    }

    // 4K60Hz只允许开到全光拼接屏/vp7
    if (tx != null && InspectCaesarUtil.getVideoResolutionType(tx)
        == VideoResolutionTypeEnum.RESOLUTION_4K_60HZ
        && !InspectCaesarUtil.isSupportHighResolution(videoWall)) {
      return Result.failureStr(NO_SUPPORT, ResponseCode.RESOLUTION_INCOMPATIBILITY_13002);
    }

    URI uri = URI.create(String.format(URI_FORMAT, getVirtualAddress(kvmMaster), PORT));
    // 全光拼接屏
    if (Objects.equals(videoWall.getDeviceModel(),
        DeviceType.CAESAR_KAITO_VIDEO_WALL.getDeviceTypeId())) {
      Collection<KaitoVideoWall.Layer> layers = new ArrayList<>();
      int groupId =
          Integer.parseInt(Property.findValueByKey(videoWall.getProperties(), "groupId", "0"));
      layers.add(caesarEntityMapper.fromPanelRect(panel));
      KaitoPanelsData panelData;
      try {
        panelData =
            cli.openKaitoPanel(uri, groupId, videoWall.getDeviceId(), layers);
      } catch (FeignException e) {
        return doFeignException(e);
      }

      Optional<KaitoVideoWall.Layer> layer = panelData.getLayers().stream().findFirst();
      if (layer.isPresent()) {
        return Result.okStr(caesarEntityMapper.toPanelRect(layer.get(), videoWall));
      } else {
        log.error("Interface did not return {}", JsonUtils.encode(panelData));
        return Result.failureStr(INTERFACE_NOT_RETURN, ResponseCode.EX_FAILURE_500);
      }
    }
    CaesarPanelRect caesarPanelRect =
        caesarEntityMapper.fromPanelRect(request.getBody().getPanelRect(),
            new KvmAssetContext(request.getMasterId()));
    List<Integer> panelIds = getVwPanelIds(getVirtualAddress(kvmMaster), videoWall.getDeviceId());
    caesarPanelRect.setPanelId(createPanelId(panelIds));
    String address = request.getKvmOperationFrom().getAddress();
    String user = request.getKvmOperationFrom().getUser();
    CaesarResponse caesarRes =
        cli.openVideoWallPanel(uri, videoWall.getDeviceId(), caesarPanelRect, address, user);
    return switch (caesarRes.getStatus()) {
      case 0, CaesarConstants.OPEN_OK -> Result.okStr(
              caesarEntityMapper.toPanelRect(caesarPanelRect, request.getMasterId()));
      case CaesarConstants.OPEN_ERR ->
          Result.failureStr(EXCEED_LIMIT, ResponseCode.OVER_OPEN_PANEL_LIMIT_13001);
      default -> Result.failureStr(UNKNOWN_CODE_FROM_SERVER, ResponseCode.EX_FAILURE_500);
    };
  }

  /**
   * closeVwPanel.
   */
  public String closeVwPanel(String msg) {
    CaesarMqRequest<PanelRectRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    URI uri = URI.create(String.format(URI_FORMAT, getVirtualAddress(kvmMaster), PORT));
    KvmVideoWall videoWall = videoWallService.getById(request.getBody().getId());
    if (videoWall.getDeviceId() < 0) {
      return Result.failureStr(ID_FORMAT_ERROR, ResponseCode.EX_FAILURE_400);
    }
    // 全光拼接屏
    if (Objects.equals(videoWall.getDeviceModel(),
        DeviceType.CAESAR_KAITO_VIDEO_WALL.getDeviceTypeId())) {
      int groupId =
          Integer.parseInt(Property.findValueByKey(videoWall.getProperties(), "groupId", "0"));
      cli.closeKaitoPanel(uri, groupId, videoWall.getDeviceId(),
          request.getBody().getPanelRect().getPanelId());
      return Result.okStr();
    }
    CaesarPanelRect caesarPanelRect =
        caesarEntityMapper.fromPanelRect(request.getBody().getPanelRect(),
            new KvmAssetContext(request.getMasterId()));
    String address = request.getKvmOperationFrom().getAddress();
    String user = request.getKvmOperationFrom().getUser();
    CaesarResponse caesarRes =
        cli.closeVideoWallPanel(uri, videoWall.getDeviceId(), caesarPanelRect, address, user);
    // todo 解码终端
    return switch (caesarRes.getStatus()) {
      case 0, CaesarConstants.CLOSE_OK -> Result.okStr(caesarPanelRect);
      case CaesarConstants.CLOSE_ERR -> Result.failureStr("关窗失败", ResponseCode.EX_FAILURE_500);
      default -> Result.failureStr(UNKNOWN_CODE_FROM_SERVER, ResponseCode.EX_FAILURE_500);
    };
  }

  /**
   * closeAllVwPanel.
   */
  public String closeAllVwPanel(String msg) {
    CaesarMqRequest<ObjectId> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    URI uri = URI.create(String.format(URI_FORMAT, getVirtualAddress(kvmMaster), PORT));
    KvmVideoWall videoWall = videoWallService.getById(request.getBody().getId());
    // 全光拼接屏
    if (Objects.equals(videoWall.getDeviceModel(),
        DeviceType.CAESAR_KAITO_VIDEO_WALL.getDeviceTypeId())) {
      int groupId =
          Integer.parseInt(Property.findValueByKey(videoWall.getProperties(), "groupId", "0"));
      cli.clearKaitoPanels(uri, groupId, videoWall.getDeviceId());
      return Result.okStr();
    }
    if (videoWall.getDeviceId() < 0) {
      return Result.failureStr(ID_FORMAT_ERROR, ResponseCode.EX_FAILURE_400);
    }
    String address = request.getKvmOperationFrom().getAddress();
    String user = request.getKvmOperationFrom().getUser();
    CaesarResponse response = cli.closeVideoWallAllPanel(uri, videoWall.getDeviceId(), address, user);
    return switch (response.getStatus()) {
      case 0, CaesarConstants.CLOSE_OK -> Result.okStr(response);
      case CaesarConstants.CLOSE_ERR -> Result.failureStr("关窗失败", ResponseCode.EX_FAILURE_500);
      default -> Result.failureStr(UNKNOWN_CODE_FROM_SERVER, ResponseCode.EX_FAILURE_500);
    };
  }

  /**
   * openVwPanels.
   */
  @Override
  public String openVwPanels(String msg) {
    CaesarMqRequest<OpenVwPanelsRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    KvmVideoWall videoWall = videoWallService.getById(request.getBody().getId());
    if (videoWall.getDeviceId() < 0) {
      return Result.failureStr(ID_FORMAT_ERROR, ResponseCode.EX_FAILURE_400);
    }
    VideoPanels videoPanels = new VideoPanels();
    videoPanels.setLayoutData(request.getBody().getLayoutData());
    videoPanels.setPanels(request.getBody().getPanelData().getPanels());
    int id = 0;
    for (PanelRect panel : videoPanels.getPanels()) {
      KvmAsset tx = kvmAssetService.getById(panel.getVideoSrcId());
      // 4K60Hz只允许开到全光拼接屏或VP7
      if (tx != null && InspectCaesarUtil.getVideoResolutionType(tx)
              == VideoResolutionTypeEnum.RESOLUTION_4K_60HZ
              && !InspectCaesarUtil.isSupportHighResolution(videoWall)) {
        return Result.failureStr(NO_SUPPORT, ResponseCode.RESOLUTION_INCOMPATIBILITY_13002);
      }
      panel.setPanelId(id++);
    }
    URI uri = URI.create(String.format(URI_FORMAT, getVirtualAddress(kvmMaster), PORT));
    // 全光拼接屏
    if (Objects.equals(videoWall.getDeviceModel(),
        DeviceType.CAESAR_KAITO_VIDEO_WALL.getDeviceTypeId())) {
      Collection<KaitoVideoWall.Layer> layers = new ArrayList<>();
      int groupId =
          Integer.parseInt(Property.findValueByKey(videoWall.getProperties(), "groupId", "0"));
      for (PanelRect panel : videoPanels.getPanels()) {
        layers.add(caesarEntityMapper.fromPanelRect(panel));
      }
      KaitoPanelsData panelData;
      try {
        panelData = cli.openKaitoPanels(uri, groupId, videoWall.getDeviceId(), layers);
      } catch (FeignException e) {
        return doFeignException(e);
      }
      videoPanels.getPanels().clear();
      panelData.getLayers().forEach(layer -> {
        PanelRect panelRect = caesarEntityMapper.toPanelRect(layer, videoWall);
        videoPanels.getPanels().add(panelRect);
      });
      redisUtil.set(getLayoutDataKeyFromRedis(kvmMaster.getMasterId()),
          JsonUtils.encode(videoPanels.getLayoutData()));
      return Result.okStr(videoPanels);
    }
    CaesarVideoPanels caesarVideoPanels =
        caesarEntityMapper.fromVideoPanels(videoPanels, new KvmAssetContext(request.getMasterId()));
    KvmOperationFrom from = request.getKvmOperationFrom();
    if (from == null) {
      from = new KvmOperationFrom("localhost", "localhost");
    }
    String address = from.getAddress();
    String user = from.getUser();
    CaesarResponse caesarRes =
        cli.openVideoWallPanels(uri, videoWall.getDeviceId(), caesarVideoPanels, address, user);
    // todo 解码终端
    return switch (caesarRes.getStatus()) {
      case 0, CaesarConstants.OPEN_OK -> Result.okStr(
              caesarEntityMapper.toVideoPanels(caesarVideoPanels, request.getMasterId()));
      case CaesarConstants.OPEN_ERR ->
              Result.failureStr(EXCEED_LIMIT, ResponseCode.OVER_OPEN_PANEL_LIMIT_13001);
      default -> Result.failureStr(UNKNOWN_CODE_FROM_SERVER, ResponseCode.EX_FAILURE_500);
    };
  }

  /**
   * moveVwPanels.
   */
  public String moveVwPanels(String msg) {
    CaesarMqRequest<PanelRectRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    URI uri = URI.create(String.format(URI_FORMAT, getVirtualAddress(kvmMaster), PORT));
    PanelRectRequestBody body = request.getBody();
    KvmVideoWall videoWall = videoWallService.getById(body.getId());
    if (videoWall.getDeviceId() < 0) {
      return Result.failureStr(ID_FORMAT_ERROR, ResponseCode.EX_FAILURE_400);
    }
    PanelRect movePanel = body.getPanelRect();
    if (Objects.equals(videoWall.getDeviceModel(),
            DeviceType.CAESAR_KAITO_VIDEO_WALL.getDeviceTypeId())) {
      int groupId =
              Integer.parseInt(Property.findValueByKey(videoWall.getProperties(), "groupId", "0"));
      VideoPanels videoPanels = getKaitoVideoVideoPanels(videoWall, uri, groupId);
      for (PanelRect panel : videoPanels.getPanels()) {
        // 换源
        if (panel.getXpos() == movePanel.getXpos()
                && panel.getPanelId() == movePanel.getPanelId()
                && panel.getYpos() == movePanel.getYpos()
                && panel.getWidth() == movePanel.getWidth()
                && panel.getHeight() == movePanel.getHeight()) {
          KaitoWriteSourceReq sourceReq = new KaitoWriteSourceReq();
          sourceReq.setSources(new ArrayList<>());
          if (movePanel.getLayoutType() == LayoutType.FOUR_SCREEN) {
            sourceReq.setLayerType(2);
            for (LayerData childPanel : movePanel.getChildPanels()) {
              KvmAsset asset = kvmAssetService.getById(childPanel.getVideoSrcId());
              if (!Objects.equals(asset.getDeviceModel(), DeviceType.CAESAR_TX.getDeviceTypeId())) {
                return Result.failureStr(NO_SUPPORT, ResponseCode.EX_FAILURE_400);
              }
              sourceReq.getSources().add(InspectCaesarUtil.buildKaitoSource(asset));
            }
          } else {
            sourceReq.setLayerType(1);
            for (LayerData childPanel : movePanel.getChildPanels()) {
              KvmAsset asset = kvmAssetService.getById(childPanel.getVideoSrcId());
              if (Objects.equals(asset.getDeviceModel(),
                      DeviceType.KAITO02_IPC.getDeviceTypeId())
                      || Objects.equals(asset.getDeviceModel(),
                      DeviceType.KAITO02_INPUT.getDeviceTypeId())) {
                sourceReq.setLayerType(0);
              }
              sourceReq.getSources().add(InspectCaesarUtil.buildKaitoSource(asset));
            }
          }
          KaitoPanelData layer;
          try {
            layer = cli.switchKaitoPanel(uri, groupId, videoWall.getDeviceId(),
                    panel.getPanelId(), sourceReq);
            return Result.okStr(caesarEntityMapper.toPanelRect(layer.getLayer(), videoWall));
          } catch (FeignException e) {
            return doFeignException(e);
          }
        }
      }
      // 移动窗口
      KaitoVideoWall.Window position = new KaitoVideoWall.Window();
      position.setX(movePanel.getXpos());
      position.setY(movePanel.getYpos());
      position.setWidth(movePanel.getWidth());
      position.setHeight(movePanel.getHeight());
      KaitoPanelData layer;
      try {
        layer = cli.moveKaitoPanel(uri, groupId, videoWall.getDeviceId(), movePanel.getPanelId(),
                position);
        return Result.okStr(caesarEntityMapper.toPanelRect(layer.getLayer(), videoWall));
      } catch (FeignException e) {
        return doFeignException(e);
      }
    }
    CaesarPanelRect caesarPanelRect =
            caesarEntityMapper.fromPanelRect(movePanel, new KvmAssetContext(request.getMasterId()));
    String address = request.getKvmOperationFrom().getAddress();
    String user = request.getKvmOperationFrom().getUser();
    caesarPanelRect.setEnableAudio(movePanel.isEnableAudio());
    CaesarResponse caesarRes =
            cli.moveVideoWallPanel(uri, videoWall.getDeviceId(), caesarPanelRect, address, user);
    return switch (caesarRes.getStatus()) {
      case 0, CaesarConstants.MOVE_OK -> Result.okStr(caesarPanelRect);
      case CaesarConstants.MOVE_ERR ->
              Result.failureStr(EXCEED_LIMIT, ResponseCode.OVER_OPEN_PANEL_LIMIT_13001);
      default -> Result.failureStr(UNKNOWN_CODE_FROM_SERVER, ResponseCode.EX_FAILURE_500);
    };
  }

  /**
   * swapVwPanelLayer.
   */
  public String swapVwPanelLayer(String msg) {
    CaesarMqRequest<SwapVwPanelLayerRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    SwapVwPanelLayerRequestBody body = request.getBody();
    KvmVideoWall videoWall = videoWallService.getById(body.getId());
    URI uri = URI.create(String.format(URI_FORMAT, getVirtualAddress(kvmMaster), PORT));
    // 全光拼接屏
    if (Objects.equals(videoWall.getDeviceModel(),
            DeviceType.CAESAR_KAITO_VIDEO_WALL.getDeviceTypeId())) {
      int groupId =
              Integer.parseInt(Property.findValueByKey(videoWall.getProperties(), "groupId", "0"));
      Action action = new Action(1);
      if (body.getSeq1() < body.getSeq2()) {
        action.setAction(2);
      }
      try {
        cli.swapKaitoPanel(uri, groupId, videoWall.getDeviceId(), body.getPanelId1(), action);
      } catch (FeignException e) {
        return doFeignException(e);
      }
      return Result.okStr();
    }
    if (videoWall.getDeviceId() < 0) {
      return Result.failureStr(ID_FORMAT_ERROR, ResponseCode.EX_FAILURE_400);
    }
    Map<String, Integer> map = new HashMap<>(4);
    map.put("panelId1", body.getPanelId1());
    map.put("seq1", body.getSeq1());
    map.put("panelId2", body.getPanelId2());
    map.put("seq2", body.getSeq2());
    String address = request.getKvmOperationFrom().getAddress();
    String user = request.getKvmOperationFrom().getUser();
    cli.swapVideoWallLayer(uri, videoWall.getDeviceId(), map, address, user);
    return Result.okStr();
  }

  /**
   * getSeatPanels.
   */
  public String getSeatPanels(String msg) {
    MqRequest<ChannelIdRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    int decoderId = request.getBody().getDecoderId();
    KvmAsset rx = kvmAssetService.oneByDeviceId(decoderId, kvmMaster.getMasterId());
    URI uri = URI.create(String.format(URI_FORMAT, getVirtualAddress(kvmMaster), PORT));
    SeatPanels panels = new SeatPanels();
    panels.setLinkStatus(true);
    panels.setPush(true);
    // 四画面Rx窗口
    if (Objects.equals(rx.getDeviceModel(), DeviceType.CAESAR_FOUR_SCREEN_RX.getDeviceTypeId())) {
      CaesarSeatPanels caesarSeatPanels = cli.getFourScreenRxPanels(uri, decoderId);
      LayoutData layoutData = InspectCaesarUtil.getFourScreenRxLayoutData(caesarSeatPanels.getLayoutType());
      layoutData.getPanels().forEach(layoutRect -> caesarSeatPanels.getPanels().forEach(p -> {
        if (layoutRect.getSeq() == p.getPanelId() + 1) {
          KvmAssetVo asset = kvmAssetService.oneByDeviceId(p.getVideoSrcId(), request.getMasterId());
          if (asset != null) {
            LayerData layer = LayerData.builder()
                    .seq(layoutRect.getSeq())
                    .videoSrcName(asset.getName())
                    .deviceType(asset.getDeviceType())
                    .ctrlMode(p.getCtrlMode())
                    .videoSrcId(asset.getAssetId()).build();
            panels.getPanels().add(layer);
          } else {
            log.warn("Got panels from four_screen_rx, {} device that doesn't exist in skylink.", p.getVideoSrcId());
          }
        }
      }));
      panels.setLayoutData(layoutData);
      panels.setWidth(layoutData.getWidth());
      panels.setHeight(layoutData.getHeight());
      return Result.okStr(panels);
    }
    List<CaesarDeviceStatus> res = cli.getDeviceStatusByDecoderId(uri, decoderId);
    res.forEach((status) -> {
      ConnectStatus connectStatus = status.getConnectStatusList().getFirst();
      if (!connectStatus.getConnectStatus().equals("disconnect")
              && status.getDeviceId() == decoderId) {
        int mode = connectStatus.getConnectStatus().equals("video") ? 2 : 1;
        int connectTxId = connectStatus.getConnectedId();
        KvmAssetVo asset = kvmAssetService.oneByDeviceId(connectTxId, request.getMasterId());
        LayerData layer = LayerData.builder()
                .videoSrcName(asset.getName())
                .deviceType(asset.getDeviceType())
                .ctrlMode(mode)
                .videoSrcId(asset.getAssetId()).build();
        panels.setPanels(Collections.singletonList(layer));
      }
    });
    return Result.okStr(panels);
  }

  public String seatOpenTxes(String msg) {
    CaesarMqRequest<SeatOpenTxesRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    KvmAssetVo rx = kvmAssetService.oneByDeviceId(request.getBody().getDecoderId(), kvmMaster.getMasterId());
    if (rx != null) {
      URI uri = URI.create(String.format(URI_FORMAT, getVirtualAddress(kvmMaster), PORT));
      Collection<PanelRect> decoderPanels = request.getBody().getPanels();
      switch (DeviceType.valueOf(rx.getDeviceType())) {
        case CAESAR_FOUR_SCREEN_RX -> {
          if (CollectionUtil.isEmpty(decoderPanels)) {
            if (request.getBody().getLayout() == null) {
              return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
            }
            Map<String, Integer> reqBody = HashMap.newHashMap(1);
            reqBody.put("layoutType", InspectCaesarUtil.layoutType(request.getBody().getLayout()));
            cli.setFourScreenRxLayout(uri, request.getBody().getDecoderId(), reqBody);
            return Result.okStr();
          }
          Collection<CaesarSeatPanelRect> panelRects = new ArrayList<>();
          decoderPanels.forEach(p -> {
            CaesarSeatPanelRect panelRect =
                    caesarEntityMapper.toSeatPanelRect(p, new KvmAssetContext(kvmMaster.getMasterId()));
            panelRects.add(panelRect);
          });
          CaesarSeatPanels caesarSeatPanels = new CaesarSeatPanels();
          caesarSeatPanels.setLayoutType(InspectCaesarUtil.layoutType(request.getBody().getLayout()));
          caesarSeatPanels.setPanels(panelRects);
          caesarSeatPanels.setLayoutType(InspectCaesarUtil.layoutType(request.getBody().getLayout()));
          cli.openFourScreenRxPanels(uri, request.getBody().getDecoderId(), caesarSeatPanels);
          return Result.okStr();
        }
        case CAESAR_RX -> {
          if (CollectionUtil.isEmpty(decoderPanels) || decoderPanels.size() != 1) {
            return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
          }
          decoderPanels.forEach(panelRect -> openTxToDecoder(panelRect.getVideoSrcId(),
                  uri, rx.getDeviceId(), panelRect.getChildPanels().getFirst().getCtrlMode(),
                  request.getKvmOperationFrom()));
          return Result.okStr();
        }
      }
    }
    return Result.failureStr(NO_SUPPORT, ResponseCode.RESOLUTION_INCOMPATIBILITY_13002);
  }

  /**
   * seatOpenTx.
   */
  public String seatOpenTx(String msg) {
    CaesarMqRequest<SeatOpenTxRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    KvmAsset tx = kvmAssetService.getById(request.getBody().getVideoSrcId());
    if (isOnHost(tx, kvmMaster)) {
      return Result.failureStr(NO_VIDEO_SRC, ResponseCode.DEVICE_NOT_IN_HOST_13003);
    }
    KvmAsset rx = kvmAssetService.oneByDeviceId(request.getBody().getDecoderId(), kvmMaster.getMasterId());
    if (rx != null) {
      int decoderId = request.getBody().getDecoderId();
      int ctrlMode = request.getBody().getCtrlMode();
      int panelId = request.getBody().getPanelId();
      VideoResolutionTypeEnum txVideoResolutionType = InspectCaesarUtil.getVideoResolutionType(tx);
      VideoResolutionTypeEnum rxVideoResolutionType = InspectCaesarUtil.getVideoResolutionType(rx);
      URI uri = URI.create(String.format(URI_FORMAT, getVirtualAddress(kvmMaster), PORT));
      if (Objects.equals(rx.getDeviceModel(), DeviceType.CAESAR_FOUR_SCREEN_RX.getDeviceTypeId())) {
        CaesarSeatPanelRect panelRect = new CaesarSeatPanelRect();
        panelRect.setVideoSrcId(tx.getDeviceId());
        panelRect.setCtrlMode(ctrlMode);
        panelRect.setPanelId(panelId);
        cli.openFourScreenRxPanel(uri, decoderId, panelRect);
        // 4K60Hz的Tx只能开到四画面Rx或者4K60Hz的Rx，其他的都能开
      } else if (txVideoResolutionType == VideoResolutionTypeEnum.RESOLUTION_4K_60HZ
              && txVideoResolutionType.getValue() > rxVideoResolutionType.getValue()) {
        return Result.failureStr(NO_SUPPORT, ResponseCode.RESOLUTION_INCOMPATIBILITY_13002);
      } else {
        openTxToDecoder(tx.getAssetId(), uri, decoderId, ctrlMode, request.getKvmOperationFrom());
      }
      return Result.okStr();
    }
    return Result.failureStr("No such decoder", ResponseCode.EX_NOTFOUND_404);
  }

  public String closeSeatPanel(String msg) {
    MqRequest<DecoderIdRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    KvmAsset rx = kvmAssetService.oneByDeviceId(request.getBody().getDecoderId(), kvmMaster.getMasterId());
    URI uri = URI.create(String.format(URI_FORMAT, getVirtualAddress(kvmMaster), PORT));
    if (rx != null && Objects.equals(rx.getDeviceModel(), DeviceType.CAESAR_FOUR_SCREEN_RX.getDeviceTypeId())) {
      Map<String, Integer> reqBody = HashMap.newHashMap(1);
      reqBody.put("panelId", request.getBody().getPanelId());
      cli.closeFourScreenRxPanel(uri, request.getBody().getDecoderId(), reqBody);
      return Result.okStr();
    }
    return Result.failureStr(NO_SUPPORT, ResponseCode.EX_FAILURE_400);
  }

  /**
   * closeAllSeatPanel.
   */
  public String closeSeatPanels(String msg) {
    CaesarMqRequest<ChannelIdRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    URI uri = URI.create(String.format(URI_FORMAT, getVirtualAddress(kvmMaster), PORT));
    // todo request.getBody().getDecoderId() -> rxId
    String address = request.getKvmOperationFrom().getAddress();
    String user = request.getKvmOperationFrom().getUser();
    cli.closeSeatAllPanel(uri, request.getBody().getDecoderId(), address, user);
    return Result.okStr();
  }

  /**
   * connectTxRx.
   */
  public String connectTxRx(String msg) {
    MqRequest<ConnectTxRxRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    URI uri = URI.create(String.format(URI_FORMAT, getVirtualAddress(kvmMaster), PORT));
    Map<String, Integer> reqBody = new HashMap<>();
    // todo request.getBody().getDecoderId() -> rxId
    //    int srcId = Converter.getCaesarAssetId(runner.getExtDeviceMap(),
    //    runner.getExtDeviceAssoMap(), object.getBody().getTxId());
    //    int conId = Converter.getCaesarAssetId(runner.getExtDeviceMap(),
    //    runner.getExtDeviceAssoMap(), object.getBody().getRxId());
    reqBody.put("mode", request.getBody().getMode());
    //    reqBody.put("cpuId", srcId);
    //    reqBody.put("conId", conId);
    cli.connectTxRx(uri, reqBody);
    return Result.okStr();
  }

  /**
   * 预览电视墙发生修改.
   *
   * @param msg .
   * @return .
   */
  public String changeVideoWall(String msg) {
    MqRequest<VideoWallChangeBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    // 预览
    previewManager.updatePreviewStatus(request.getBody().getDeviceType(), kvmMaster.getMasterId());
    return Result.okStr();
  }

  /**
   * 四画面扩展设备发生修改.
   *
   * @param msg .
   * @return .
   */
  public String changeExtend(String msg) {
    MqRequest<ExtendChangeBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    previewManager.updatePreviewStatus(request.getBody().getDeviceType(), kvmMaster.getMasterId());
    return Result.okStr();
  }

  public String refreshExtendVersion(String msg) {
    MqRequest<IdsRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    Map<Integer, List<KvmAsset>> map = kvmAssetService.listByIds(request.getBody().getIds())
            .stream().collect(Collectors.groupingBy(KvmAsset::getDeviceModel));
    map.forEach((k, v) -> {
      if (Objects.equals(k, DeviceType.CAESAR_VP7.getDeviceTypeId())) {
        vp7CmdServer.getVp7VersionAndUpdateDevice(v);
      }
    });
    return Result.okStr();
  }

  /**
   * 整屏回显取流.
   */
  public String openStreaming(String msg) {
    MqRequest<TxConnectRequest> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    return Result.okStr(previewManager.onGetStreamingResource(kvmMaster, request.getBody()));
  }

  /**
   * getSnapshot.
   */
  public String getSnapshot(String msg) {
    MqRequest<TxIdRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    RedundantModeEnum mode = getRedundantMode(kvmMaster.getMasterId());
    String relatedId = InspectCaesarUtil.getRelatedMasterId(kvmMaster);
    String txId = request.getBody().getTxId();
    if (!relatedId.equals(kvmMaster.getMasterId()) && mode == RedundantModeEnum.BACKUP) {
      KvmMaster relatedMaster = kvmMasterService.getById(relatedId);
      if (relatedMaster != null) {
        kvmMaster = relatedMaster;
      } else {
        return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
      }
      KvmAsset tx = kvmAssetService.getById(txId);
      KvmAsset backTx = kvmAssetService.oneByDeviceId(tx.getDeviceId(), kvmMaster.getMasterId());
      txId = backTx.getAssetId();
    }
    previewManager.onGetSnapshot(kvmMaster.getMasterId(), txId);
    return Result.okStr();
  }

  /**
   * 连接编码器.
   *
   * @param msg 消息体.
   * @return 编码器信息.
   */
  public String connectToEncoder(String msg) {
    MqRequest<TxIdRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    // 根据KVMMaster对象的ID获取关联的编码器对象
    EncoderAssoVo encoderAsso = encoderAssoService.oneByMasterId(kvmMaster.getMasterId());
    Map<String, Object> kvmControlEnable = Maps.newHashMap();
    if (StringUtils.isNotBlank(encoderAsso.getRtspUrl())) {
      kvmControlEnable.put("kvmControlEnable", true);
      kvmControlEnable.put("resolutionWidth", 1920);
      kvmControlEnable.put("resolutionHeight", 1080);
      kvmControlEnable.put("address", encoderAsso.getRtspUrl());
      URI encoderUri = URI.create(String.format(URI_FORMAT, encoderAsso.getIp(), 80));
      EncoderEdidParam edid = cli.getEncoderEdid(encoderUri);
      EncoderDetailParam detail = cli.getEncoderDetail(encoderUri);
      Optional<OptionDto> opt =
              edid.getEdidList().stream().filter(e -> e.getValue().equals(detail.getEdidTypeStr()))
                      .findFirst();
      if (opt.isPresent()) {
        String[] wh = opt.get().getOption().split("x");
        kvmControlEnable.put("resolutionWidth", Integer.valueOf(wh[0].trim(), 10));
        kvmControlEnable.put("resolutionHeight", Integer.valueOf(wh[1].trim(), 10));
      }
      KvmAsset tx = kvmAssetService.getById(request.getBody().getTxId());
      KvmAsset rx = kvmAssetService.getById(encoderAsso.getRxId());
      Map<String, Integer> reqBody = new HashMap<>();
      // 操作模式连接
      reqBody.put("mode", 0);
      reqBody.put("cpuId", tx.getDeviceId());
      reqBody.put("conId", rx.getDeviceId());
      URI txRxUri = URI.create(String.format(URI_FORMAT, getVirtualAddress(kvmMaster), PORT));
      cli.connectTxRx(txRxUri, reqBody);
      return Result.okStr(kvmControlEnable);
    } else {
      kvmControlEnable.put("kvmControlEnable", false);
      return Result.failureStr(JsonUtils.encode(kvmControlEnable), ResponseCode.EX_FAILURE_400);
    }
  }

  /**
   * 获取横幅信息.
   */
  public String getBannerInfo(String msg) {
    MqRequest<ObjectId> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    KvmVideoWall wall = videoWallService.getById(request.getBody().getId());
    if (wall != null) {
      URI uri = URI.create(String.format(URI_FORMAT, getVirtualAddress(kvmMaster), PORT));
      switch (wall.getBannerType()) {
        case BANNER_KAITO -> {
          int groupId = Integer.parseInt(Property.findValueByKey(wall.getProperties(), "groupId", "0"));
          KaitoGroupData groupData = cli.getKaitoVideoWallGroup(uri, groupId);
          URI kaitoUrl = URI.create(String.format(URI_FORMAT, groupData.getIp(), groupData.getPort()));
          KaitoScreenDetail screenDetail = caesarKaitoCmd.getScreenReadDetail(kaitoUrl, groupData, wall.getDeviceId());
          if (screenDetail != null) {
            // 需要计算上偏移量
            int posX = screenDetail.getOutputMode().getSize().getX();
            int posY = screenDetail.getOutputMode().getSize().getY();
            screenDetail.getOsd().setX(screenDetail.getOsd().getX() - posX);
            screenDetail.getOsd().setY(screenDetail.getOsd().getY() - posY);
            return Result.okStr(screenDetail.getOsd());
          } else {
            return Result.failureStr("Get osd data failed!", ResponseCode.EX_FAILURE_500);
          }
        }
        case BANNER_VP7 -> {
          return getVp7BannerInfo(wall);
        }
        default -> {
          return Result.failureStr(NO_SUPPORT, ResponseCode.EX_FAILURE_400);
        }
      }
    } else {
      return Result.failureStr(NO_VIDEO_WALL, ResponseCode.EX_FAILURE_400);
    }
  }

  /**
   * 开启/关闭横幅.
   */
  public String enableBanner(String msg) {
    CaesarMqRequest<VwEnableRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    KvmVideoWall wall = videoWallService.getById(request.getBody().getId());
    URI uri = URI.create(String.format(URI_FORMAT, getVirtualAddress(kvmMaster), PORT));
    try {
      if (wall != null) {
        boolean enable = request.getBody().isEnable();
        switch (wall.getBannerType()) {
          case BANNER_KAITO -> {
            int groupId = Integer.parseInt(Property.findValueByKey(wall.getProperties(), "groupId", "0"));
            KaitoGroupData groupData = cli.getKaitoVideoWallGroup(uri, groupId);
            URI kaitoUrl = URI.create(String.format(URI_FORMAT, groupData.getIp(), groupData.getPort()));
            return Result.okStr(caesarKaitoCmd.enableBanner(kaitoUrl, groupData, wall.getDeviceId(), enable));
          }
          case BANNER_VP7 -> {
            CaesarSwitchBanner switchBanner = new CaesarSwitchBanner();
            switchBanner.setEnableBanner(enable);
            String addr = request.getKvmOperationFrom().getAddress();
            String name = request.getKvmOperationFrom().getUser();
            cli.switchBanner(uri, wall.getDeviceId(), switchBanner, addr, name);
            Collection<VideoWallBanner> banners = new ArrayList<>();
            bannerService.listByVideoWallId(wall.getDeviceId()).forEach(bs -> {
              if (bs.isStatus() != enable)
              bs.getContent().setEnable(enable);
              banners.add(bs);
            });
            bannerService.updateBatchById(banners);
            return getVp7BannerInfo(wall);
          }
          default -> {
            return Result.failureStr(NO_SUPPORT, ResponseCode.EX_FAILURE_400);
          }
        }
      }
      return Result.failureStr(NO_VIDEO_WALL, ResponseCode.EX_FAILURE_400);
    } catch (FeignException e) {
      return doFeignException(e);
    }
  }

  /**
   * 开启/关闭横幅背景色.
   */
  public String enableBannerBgColor(String msg) {
    CaesarMqRequest<VwEnableRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    KvmVideoWall wall = videoWallService.getById(request.getBody().getId());
    if (wall != null && wall.getBannerType() == BannerType.BANNER_VP7) {
      CaesarSwitchBannerColor switchBanner = new CaesarSwitchBannerColor();
      switchBanner.setEnableBannerBg(request.getBody().isEnable());
      String addr = request.getKvmOperationFrom().getAddress();
      String name = request.getKvmOperationFrom().getUser();
      URI uri = URI.create(String.format(URI_FORMAT, getVirtualAddress(kvmMaster), PORT));
      cli.switchBannerBgColor(uri, wall.getDeviceId(), switchBanner, addr, name);
      return Result.okStr();
    }
    return Result.failureStr(NO_SUPPORT, ResponseCode.EX_FAILURE_400);
  }

  /**
   * 设置大屏横幅.
   */
  public String setBanner(String msg) {
    MqRequest<VwBannerRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    KvmVideoWall wall = videoWallService.getById(request.getBody().getWallId());
    VideoWallBanner banner = bannerService.getById(request.getBody().getBannerId());
    if (wall != null && banner != null && BannerType.BANNER_VP7 == wall.getBannerType()) {
      banner.setStatus(request.getBody().isEnable());
      banner.getContent().setEnable(request.getBody().isEnable());
      return vp7CmdServer.setBannerConfig(wall, banner);
    }
    return Result.failureStr(NO_SUPPORT, ResponseCode.EX_FAILURE_400);
  }

  public String updateClient(String msg) {
    MqRequest<Heartbeat> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    previewManager.onUpdateClient(request.getMasterId(), request.getBody().getClientType(),
            request.getBody().getJwtId());
    return Result.okStr();
  }

  /**
   * 上传大屏底部图片.
   */
  public String uploadVideoWallBottomImage(String msg) {
    MqRequest<ImageRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    KvmVideoWall wall = videoWallService.getById(request.getBody().getWallId());
    if (Objects.equals(DeviceType.CAESAR_VP7_VIDEO_WALL.getDeviceTypeId(), wall.getDeviceModel())) {
      return vp7CmdServer.uploadBottomImage(wall, request.getBody().getHash());
    }
    return Result.failureStr(NO_SUPPORT, ResponseCode.EX_FAILURE_400);
  }

  /**
   * 开启或关闭显示大屏底图.
   */
  public String enableVideoWallBottomImage(String msg) {
    CaesarMqRequest<VwEnableRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    KvmVideoWall wall = videoWallService.getById(request.getBody().getId());
    if (wall != null) {
      CaesarSwitchBottomImage switchBottomImage = new CaesarSwitchBottomImage();
      switchBottomImage.setEnableBgImg(request.getBody().isEnable());
      String addr = request.getKvmOperationFrom().getAddress();
      String name = request.getKvmOperationFrom().getUser();
      URI uri = URI.create(String.format(URI_FORMAT, getVirtualAddress(kvmMaster), PORT));
      cli.switchBannerBottomImage(uri, wall.getDeviceId(), switchBottomImage, addr, name);
      return Result.okStr();
    }
    return Result.failureStr(NO_VIDEO_WALL, ResponseCode.EX_FAILURE_400);
  }

  /**
   * 取消外设升级.
   */
  public String cancelUpgradeAsset(String msg) {
    MqRequest<PeripheralUpgradeIdRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmAsset asset = kvmAssetService.getById(request.getBody().getDeviceId());
    boolean result = true;
    if (asset != null && Objects.equals(DeviceType.CAESAR_VP7.getDeviceTypeId(), asset.getDeviceModel())
            && StringUtils.isNotBlank(asset.getDeviceIp())) {
      result = vp7CmdServer.cancelUpgrade(asset, request.getBody().getTaskId());
    }
    return result ? Result.okStr() : Result.failureStr("Cancel Failed.", ResponseCode.EX_FAILURE_500);
  }

  /**
   * 外设升级.
   */
  public String startUpgradeAsset(String msg) {
    MqRequest<PeripheralUpgradeTaskBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    PeripheralUpgradeTaskBody body = request.getBody();
    Map<String, Boolean> res = new HashMap<>();
    for (String assetId : body.getAssetIdsAndTaskIds().keySet()) {
      KvmAsset asset = kvmAssetService.getById(assetId);
      if (InspectCaesarUtil.isAvailableVp7(asset)) {
        boolean result = vp7CmdServer.startUpgrade(asset, body.getPackageUrl(), body.getAuth(),
                body.getAssetIdsAndTaskIds().get(assetId));
        res.put(asset.getName(), result);
      }
    }
    return Result.okStr(JsonUtils.encode(res));
  }

  /**
   * 手动更新外设升级状态.
   */
  public String checkUpgradeAsset(String msg) {
    MqRequest<PeripheralUpgradeIdRequestBody> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr(PARAM_ERR, ResponseCode.EX_FAILURE_400);
    }
    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr(NO_HOST, ResponseCode.EX_NOTFOUND_404);
    }
    KvmAsset asset = kvmAssetService.getById(request.getBody().getDeviceId());
    if (InspectCaesarUtil.isAvailableVp7(asset)) {
      return Result.okStr(vp7CmdServer.getVp7VersionAndUpdateTask(asset, request.getBody().getTaskId()));
    }
    return Result.failureStr("Check Failed.", ResponseCode.EX_FAILURE_500);
  }


  /**
   * 集群时，若负责预览任务，需要定时刷新，确保任务存活.
   */
  public void refreshPreviewStatus(Collection<KvmMaster> caesarMasters) {
    Set<String> caesarMasterIds =
            caesarMasters.stream().map(KvmMaster::getMasterId).collect(Collectors.toSet());
    previewManager.refreshStreamingResourceStatus(caesarMasterIds);
    previewManager.refreshSnapshotPreviewStatus(caesarMasterIds);
  }

  /**
   * 读取冗余状态.
   */
  public void refreshRedundantMode(Collection<KvmMaster> caesarMasters) {
    caesarMasters.forEach(master -> {
      URI uri = URI.create(String.format(URI_FORMAT, master.getDeviceIp(), PORT));
      CaesarRedundantMode mode = new CaesarRedundantMode();
      try {
        mode.setState(cli.getRedundantMode(uri).getState());
      } catch (Exception e) {
        log.error(e.getMessage(), e);
        // 离线时
        if (!InspectCaesarUtil.getRelatedMasterId(master).equals(master.getMasterId())) {
          setRedundantMode(master.getMasterId(), RedundantModeEnum.BACKUP);
        } else {
          setRedundantMode(master.getMasterId(), RedundantModeEnum.ALONE);
        }
        return;
      }
      // 在线时
      RedundantModeEnum currentModeValue = RedundantModeEnum.getMode(mode.getState());
      RedundantModeEnum oldMode = getRedundantMode(master.getMasterId());
      if (oldMode != currentModeValue) {
        setRedundantMode(master.getMasterId(), currentModeValue);
        log.info(String.format("Caesar %s change redundant mode: %s -> %s", master.getName(),
                oldMode, currentModeValue));
      }
    });
  }

  private String getVp7BannerInfo(KvmVideoWall wall) {
    return vp7CmdServer.getBannerConfig(wall);
  }
  /**
   * 生成开窗的窗口Id.
   *
   * @param panelIds 电视墙已有的窗口Id.
   * @return 新的不重复的窗口Id.
   */
  private int createPanelId(List<Integer> panelIds) {
    if (panelIds.isEmpty()) {
      return 0;
    }
    int panelId = Collections.max(panelIds) + 1;
    int maxPanelId = 200;
    if (panelId > maxPanelId) {
      panelId = 0;
      while (panelIds.contains(panelId)) {
        panelId++;
      }
    }
    return panelId;
  }

  /**
   * 获取指定电视墙当前的所有窗口Id.
   *
   * @return 窗口Id的list集合.
   */
  private List<Integer> getVwPanelIds(String masterIp, Integer caesarVideoWallId) {
    List<Integer> panelIds = new ArrayList<>();
    URI uri = URI.create(String.format(URI_FORMAT, masterIp, PORT));
    CaesarVideoPanels videoWallPanels = cli.getVideoWallPanels(uri, caesarVideoWallId);
    for (CaesarPanelRect panel : videoWallPanels.getPanels()) {
      panelIds.add(panel.getPanelId());
    }
    return panelIds;
  }

  private boolean isOnHost(KvmAsset tx, KvmMaster master) {
    return tx == null || !tx.getMasterId().equals(master.getMasterId());
  }

  private String getLayoutDataKeyFromRedis(String masterId) {
    return RedisUtil.redisKey(RedisKey.KVM, "layout.data", masterId);
  }

  private VideoPanels getKaitoVideoVideoPanels(KvmVideoWall videoWall, URI uri, int groupId) {
    Collection<KaitoVideoWall.Layer> panelData =
            cli.getKaitoVideoWall(uri, groupId, videoWall.getDeviceId()).getLayers();
    VideoPanels videoPanels = new VideoPanels();
    panelData.forEach(layer -> {
      PanelRect panelRect = caesarEntityMapper.toPanelRect(layer, videoWall);
      videoPanels.getPanels().add(panelRect);
    });
    videoPanels.setLayoutData(redisUtil.getStr(getLayoutDataKeyFromRedis(videoWall.getMasterId()))
            .map(strData -> JsonUtils.decode(strData, LayoutData.class)).orElse(new LayoutData()));
    return videoPanels;
  }

  private String doFeignException(FeignException e) {
    log.error("Cmd to caesar-kaito failed!", e);
    Optional<ByteBuffer> body = e.responseBody();
    if (body.isPresent()) {
      Charset utf8 = StandardCharsets.UTF_8;
      CaesarResponse err = JsonUtils.decode(utf8.decode(body.get()).toString(),
              CaesarResponse.class);
      return InspectCaesarUtil.getResultFromApiStatus(err);
    } else {
      log.error("Can not decode caesar error response! {}", e.getMessage());
      return Result.failureStr(INTERFACE_NOT_RETURN, ResponseCode.EX_FAILURE_500);
    }
  }

  /**
   * 获取虚拟Ip, 不存在时返回主机的Ip.
   */
  private String getVirtualAddress(KvmMaster master) {
    return Property.findValueByKey(master.getCollectorProperties(), "virtualAddress", master.getDeviceIp());
  }

  private RedundantModeEnum getRedundantMode(String masterId) {
    return redisUtil.getStr(RedisUtil.redisKey(RedisKey.REDUNDANT_STATUS,
            masterId)).map(RedundantModeEnum::valueOf).orElse(RedundantModeEnum.ALONE);
  }

  private void setRedundantMode(String masterId, RedundantModeEnum mode) {
    redisUtil.set(RedisUtil.redisKey(RedisKey.REDUNDANT_STATUS, masterId),
            String.valueOf(mode));
  }

  /**
   * 调用型号为CAESAR_RX的坐席屏幕，打开一个全屏Tx.
   *
   * @param videoSrcId txId.
   */
  private void openTxToDecoder(String videoSrcId, URI uri, int decoderId, int ctrlMode, KvmOperationFrom from) {
    KvmAsset asset = kvmAssetService.getById(videoSrcId);
    if (asset == null) {
      return;
    }
    Map<String, Integer> map = new HashMap<>();
    map.put("videoSrcId", asset.getDeviceId());
    map.put("ctrlMode", ctrlMode);
    String address = from.getAddress();
    String user = from.getUser();
    cli.openTx(uri, decoderId, map, address, user);
  }
}
