package com.mediacomm.caesar.domain;

import com.mediacomm.entity.message.LayoutIpcData;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * CaesarPanelRect.
 */
@Data
public class CaesarPanelRect {
  private int panelId;
  private int seq;
  private int xpos;
  private int ypos;
  private int width;
  private int height;
  private int ctrlMode;
  private int videoSrcId;
  private boolean enableAudio;
  private List<LayoutIpcData> layouts = new ArrayList<>();
}
