package com.mediacomm.gbgateway.controller;

import static com.mediacomm.system.variable.ResponseCode.EX_FAILURE_400;
import static com.mediacomm.system.variable.ResponseCode.EX_FAILURE_500;
import static com.mediacomm.system.variable.ResponseCode.EX_NOTFOUND_404;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.mediacomm.entity.Property;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.message.reqeust.MqRequest;
import com.mediacomm.gbgateway.api.GbGatewayDeviceApi;
import com.mediacomm.gbgateway.entity.DeviceListRequest;
import com.mediacomm.gbgateway.entity.DeviceListResponse;
import com.mediacomm.gbgateway.entity.GbGatewayDevice;
import com.mediacomm.gbgateway.util.mapper.GbGatewayEntityMapper;
import com.mediacomm.system.base.kvm.CmdServer;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.util.JsonUtils;
import com.mediacomm.util.RedisUtil;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * GB Gateway命令服务器.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class GbGatewayCmdServer extends CmdServer {

  @Resource
  private KvmMasterService kvmMasterService;

  @Resource
  private KvmAssetService kvmAssetService;

  @Resource
  private GbGatewayEntityMapper gbGatewayEntityMapper;

  @Resource
  private RedisUtil redisUtil;

  /**
   * 刷新配置.
   *
   * @param msg 消息内容
   * @return 处理结果
   */
  public String refreshConfig(String msg) {
    MqRequest<?> request = JsonUtils.decode(msg, new TypeReference<>() {
    });
    if (request == null) {
      return Result.failureStr("Invalid request", EX_FAILURE_400);
    }

    KvmMaster kvmMaster = kvmMasterService.getById(request.getMasterId());
    if (kvmMaster == null) {
      return Result.failureStr("Master not found", EX_NOTFOUND_404);
    }

    try {
      refreshDeviceList(kvmMaster);
      return Result.okStr("Config refreshed successfully");
    } catch (Exception e) {
      log.error("Failed to refresh GB Gateway config for master: {}", request.getMasterId(), e);
      return Result.failureStr("Failed to refresh config: " + e.getMessage(), EX_FAILURE_500);
    }
  }

  /**
   * 刷新设备列表.
   */
  public void refreshDeviceList() {
    Collection<KvmMaster> kvmMasters =
        kvmMasterService.allByDeviceModel(DeviceType.GB_GATEWAY.getDeviceTypeId());

    for (KvmMaster kvmMaster : kvmMasters) {
      try {
        refreshDeviceList(kvmMaster);
      } catch (Exception e) {
        log.error("Failed to refresh device list for master: {}", kvmMaster.getMasterId(), e);
      }
    }
  }

  /**
   * 刷新指定主机的设备列表.
   *
   * @param kvmMaster KVM主机
   * @throws Exception 异常
   */
  private void refreshDeviceList(KvmMaster kvmMaster) throws Exception {
    String baseUrl = getGbGatewayBaseUrl(kvmMaster);
    String platformId = getGbGatewayPlatformId(kvmMaster);

    log.info("Refreshing device list for GB Gateway master: {}, baseUrl: {}, platformId: {}",
        kvmMaster.getMasterId(), baseUrl, platformId);

    // 测试连接
    if (!GbGatewayDeviceApi.testConnection(baseUrl)) {
      throw new Exception("Cannot connect to GB Gateway: " + baseUrl);
    }

    List<GbGatewayDevice> allDevices = getAllDevices(baseUrl, platformId);

    // 保存设备信息（只新增和更新，不删除不存在的设备）
    for (GbGatewayDevice device : allDevices) {
      saveGbGatewayDevice(device, kvmMaster);
      // 保存设备在线状态到Redis
      saveDeviceStatusToRedis(device, kvmMaster);
    }

    log.info("Successfully refreshed {} GB Gateway devices for master: {}",
        allDevices.size(), kvmMaster.getMasterId());
  }

  /**
   * 获取所有设备.
   *
   * @param baseUrl    GB Gateway基础URL
   * @param platformId 平台ID
   * @return 设备列表
   * @throws Exception 异常
   */
  private List<GbGatewayDevice> getAllDevices(String baseUrl, String platformId) throws Exception {
    List<GbGatewayDevice> allDevices = Lists.newArrayList();
    int pageSize = 100;
    int currentPage = 1;

    while (true) {
      DeviceListRequest request = DeviceListRequest.builder()
          .platformId(platformId)
          .page(currentPage)
          .pageSize(pageSize)
          .build();

      DeviceListResponse response = GbGatewayDeviceApi.getDeviceList(baseUrl, request);

      if (response.getCode() != 200 || response.getData() == null) {
        throw new Exception("Failed to get device list: " + response.getMessage());
      }

      List<GbGatewayDevice> devices = response.getData().getDevices();
      if (devices == null || devices.isEmpty()) {
        break;
      }

      allDevices.addAll(devices);

      // 检查是否还有更多页
      if (devices.size() < pageSize) {
        break;
      }

      currentPage++;
    }

    return allDevices;
  }

  /**
   * 保存GB Gateway设备.
   *
   * @param device    GB Gateway设备信息
   * @param kvmMaster KVM主机
   */
  private void saveGbGatewayDevice(GbGatewayDevice device, KvmMaster kvmMaster) {
    KvmAsset kvmAsset = gbGatewayEntityMapper.toKvmAsset(device, kvmMaster);
    kvmAssetService.saveOrUpdateKvmAsset(kvmAsset, kvmMaster.getMasterId());
  }

  /**
   * 保存设备在线状态到Redis.
   *
   * @param device    GB Gateway设备信息
   * @param kvmMaster KVM主机
   */
  private void saveDeviceStatusToRedis(GbGatewayDevice device, KvmMaster kvmMaster) {
    try {
      String deviceKey = String.format("gbgateway:device:status:%s:%s",
          kvmMaster.getMasterId(), device.getDeviceId());

      // 设备状态信息
      String statusInfo = String.format(
          "{\"deviceId\":\"%s\",\"status\":%d,\"lastUpdate\":\"%s\",\"masterId\":\"%s\"}",
          device.getDeviceId(),
          device.getStatus() != null ? device.getStatus() : 0,
          LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
          kvmMaster.getMasterId());

      // 设置过期时间为5分钟（300秒），如果设备长时间未更新则自动过期
      redisUtil.set(deviceKey, statusInfo, 300);

      log.debug("Saved device status to Redis: {} -> {}", deviceKey, statusInfo);
    } catch (Exception e) {
      log.error("Failed to save device status to Redis for device: {}", device.getDeviceId(), e);
    }
  }

  /**
   * 获取GB Gateway基础URL.
   *
   * @param kvmMaster KVM主机
   * @return 基础URL
   */
  private String getGbGatewayBaseUrl(KvmMaster kvmMaster) {
    String protocol =
        Property.findValueByKey(kvmMaster.getCollectorProperties(), "protocol", "http");
    String port = Property.findValueByKey(kvmMaster.getCollectorProperties(), "port", "8080");
    return String.format("%s://%s:%s", protocol, kvmMaster.getDeviceIp(), port);
  }

  /**
   * 获取GB Gateway平台ID.
   *
   * @param kvmMaster KVM主机
   * @return 平台ID
   */
  private String getGbGatewayPlatformId(KvmMaster kvmMaster) {
    return Property.findValueByKey(kvmMaster.getCollectorProperties(), "platformId", "");
  }

  @Override
  public String openVwPanels(String msg) {
    return "";
  }
}
