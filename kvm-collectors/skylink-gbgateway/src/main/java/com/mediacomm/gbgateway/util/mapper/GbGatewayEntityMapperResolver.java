package com.mediacomm.gbgateway.util.mapper;

import com.mediacomm.entity.Property;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.Version;
import com.mediacomm.gbgateway.entity.GbGatewayDevice;
import com.mediacomm.system.variable.sysenum.DeviceType;
import com.mediacomm.util.SkyLinkStringUtil;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.BeforeMapping;
import org.mapstruct.MappingTarget;
import org.springframework.stereotype.Component;

/**
 * GB Gateway实体映射器解析器.
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class GbGatewayEntityMapperResolver {

  /**
   * 映射前处理，设置KvmAsset的基础信息.
   *
   * @param device    GB Gateway设备信息
   * @param kvmAsset  目标KvmAsset对象
   * @param kvmMaster KVM主机信息
   */
  @BeforeMapping
  public void toKvmAssetBefore(GbGatewayDevice device, @MappingTarget KvmAsset kvmAsset,
                               KvmMaster kvmMaster) {
    // 设置基础信息
    kvmAsset.setAssetId(SkyLinkStringUtil.uuid());
    kvmAsset.setMasterId(kvmMaster.getMasterId());

    // 设置硬件编码，格式：masterId.gbgateway.deviceId
    kvmAsset.setHardcode(String.format("%s.%s",
        kvmMaster.getMasterId(), device.getDeviceId()));

    // 设置设备类型为GB Gateway IPC类型
    kvmAsset.setDeviceModel(DeviceType.GB_GATEWAY_IPC.getDeviceTypeId());

    // 设置设备属性
    List<Property> properties = new ArrayList<>();

    if (device.getDeviceType() != null) {
      properties.add(new Property("deviceType", device.getDeviceType()));
    }

    if (device.getManufacturer() != null) {
      properties.add(new Property("manufacturer", device.getManufacturer()));
    }

    if (device.getModel() != null) {
      properties.add(new Property("model", device.getModel()));
    }

    if (device.getDescription() != null) {
      properties.add(new Property("description", device.getDescription()));
    }

    if (device.getLocation() != null) {
      properties.add(new Property("location", device.getLocation()));
    }

    if (device.getStatus() != null) {
      properties.add(new Property("status", device.getStatus().toString()));
    }

    if (device.getPort() != null) {
      properties.add(new Property("port", device.getPort().toString()));
    }

    if (device.getChannelCount() != null) {
      properties.add(new Property("channelCount", device.getChannelCount().toString()));
    }

    if (device.getCodec() != null) {
      properties.add(new Property("codec", device.getCodec()));
    }

    if (device.getResolution() != null) {
      properties.add(new Property("resolution", device.getResolution()));
    }

    if (device.getFrameRate() != null) {
      properties.add(new Property("frameRate", device.getFrameRate().toString()));
    }

    if (device.getBitRate() != null) {
      properties.add(new Property("bitRate", device.getBitRate().toString()));
    }

    if (device.getCreateTime() != null) {
      properties.add(new Property("createTime", device.getCreateTime()));
    }

    if (device.getUpdateTime() != null) {
      properties.add(new Property("updateTime", device.getUpdateTime()));
    }

    kvmAsset.setProperties(properties);

    // 设置版本信息
    List<Version> versions = new ArrayList<>();
    if (device.getVersion() != null) {
      versions.add(new Version("firmware", device.getVersion(),
          device.getUpdateTime() != null ? device.getUpdateTime() : ""));
    }
    kvmAsset.setVersion(versions);

    // 设置采集器属性（可以为空，后续可根据需要添加）
    kvmAsset.setCollectorProperties(new ArrayList<>());

    log.debug("Mapped GB Gateway device {} to KvmAsset with hardcode: {}",
        device.getDeviceId(), kvmAsset.getHardcode());
  }
}
