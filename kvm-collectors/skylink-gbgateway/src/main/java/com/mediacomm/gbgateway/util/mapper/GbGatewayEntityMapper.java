package com.mediacomm.gbgateway.util.mapper;

import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.gbgateway.entity.GbGatewayDevice;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * GB Gateway实体映射器.
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", uses = GbGatewayEntityMapperResolver.class)
public interface GbGatewayEntityMapper {

  GbGatewayEntityMapper INSTANCE = Mappers.getMapper(GbGatewayEntityMapper.class);

  /**
   * 将GB Gateway设备信息映射为KvmAsset.
   *
   * @param device    GB Gateway设备信息
   * @param kvmMaster KVM主机信息
   * @return KvmAsset实体
   */
  @Mapping(source = "device.name", target = "alias")
  @Mapping(source = "device.name", target = "name")
  @Mapping(source = "device.ip", target = "deviceIp")
  @Mapping(source = "device.deviceId", target = "hardcode")
  @Mapping(source = "kvmMaster.masterId", target = "masterId")
  @Mapping(target = "assetId", ignore = true)
  @Mapping(target = "deviceModel", ignore = true)
  @Mapping(target = "properties", ignore = true)
  @Mapping(target = "collectorProperties", ignore = true)
  @Mapping(target = "version", ignore = true)
  @Mapping(target = "deviceId", ignore = true)
  KvmAsset toKvmAsset(GbGatewayDevice device, KvmMaster kvmMaster);
}
