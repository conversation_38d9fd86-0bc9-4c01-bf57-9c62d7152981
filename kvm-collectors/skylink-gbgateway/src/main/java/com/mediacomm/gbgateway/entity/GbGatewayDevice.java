package com.mediacomm.gbgateway.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * GB Gateway设备信息实体类.
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
public class GbGatewayDevice {
  /**
   * 设备ID.
   */
  private String deviceId;

  /**
   * 设备名称.
   */
  private String name;

  /**
   * 设备类型.
   */
  private String deviceType;

  /**
   * 父设备ID.
   */
  private String parentId;

  /**
   * 设备IP地址.
   */
  private String ip;

  /**
   * 设备端口.
   */
  private Integer port;

  /**
   * 设备状态 (0-离线, 1-在线).
   */
  private Integer status;

  /**
   * 设备厂商.
   */
  private String manufacturer;

  /**
   * 设备型号.
   */
  private String model;

  /**
   * 设备版本.
   */
  private String version;

  /**
   * 设备描述.
   */
  private String description;

  /**
   * 设备位置.
   */
  private String location;

  /**
   * 创建时间.
   */
  private String createTime;

  /**
   * 更新时间.
   */
  private String updateTime;

  /**
   * 设备通道数.
   */
  private Integer channelCount;

  /**
   * 设备编码格式.
   */
  private String codec;

  /**
   * 设备分辨率.
   */
  private String resolution;

  /**
   * 设备帧率.
   */
  private Integer frameRate;

  /**
   * 设备码率.
   */
  private Integer bitRate;
}
