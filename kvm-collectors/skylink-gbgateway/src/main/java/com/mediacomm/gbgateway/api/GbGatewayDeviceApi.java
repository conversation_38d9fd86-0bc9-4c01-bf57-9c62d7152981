package com.mediacomm.gbgateway.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mediacomm.gbgateway.entity.DeviceListRequest;
import com.mediacomm.gbgateway.entity.DeviceListResponse;
import com.mediacomm.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * GB Gateway设备API接口类.
 *
 * <AUTHOR>
 */
@Slf4j
public class GbGatewayDeviceApi {

  private static final RestTemplate restTemplate = new RestTemplate();
  private static final ObjectMapper objectMapper = new ObjectMapper();

  /**
   * 获取摄像头设备列表.
   *
   * @param baseUrl GB Gateway服务器地址
   * @param request 查询请求参数
   * @return 设备列表响应
   * @throws Exception 异常
   */
  public static DeviceListResponse getDeviceList(String baseUrl, DeviceListRequest request)
      throws Exception {
    try {
      String url = buildDeviceListUrl(baseUrl, request);

      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_JSON);
      headers.set("Accept", MediaType.APPLICATION_JSON_VALUE);

      HttpEntity<String> entity = new HttpEntity<>(headers);

      log.info("Calling GB Gateway device list API: {}", url);

      ResponseEntity<String> response = restTemplate.exchange(
          url,
          HttpMethod.GET,
          entity,
          String.class
      );

      if (response.getStatusCode() == HttpStatus.OK) {
        String responseBody = response.getBody();
        log.debug("GB Gateway API response: {}", responseBody);

        return JsonUtils.decode(responseBody, DeviceListResponse.class);
      } else {
        throw new Exception("GB Gateway API call failed with status: " + response.getStatusCode());
      }

    } catch (Exception e) {
      log.error("Error calling GB Gateway device list API", e);
      throw new Exception("Failed to get device list from GB Gateway: " + e.getMessage(), e);
    }
  }

  /**
   * 构建设备列表查询URL.
   *
   * @param baseUrl 基础URL
   * @param request 查询请求
   * @return 完整的查询URL
   */
  private static String buildDeviceListUrl(String baseUrl, DeviceListRequest request) {
    UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(baseUrl)
        .path("/api/devices");

    if (request.getPlatformId() != null) {
      builder.queryParam("platformId", request.getPlatformId());
    }

    if (request.getPage() != null) {
      builder.queryParam("page", request.getPage());
    }

    if (request.getPageSize() != null) {
      builder.queryParam("pageSize", request.getPageSize());
    }

    if (request.getDeviceType() != null) {
      builder.queryParam("deviceType", request.getDeviceType());
    }

    if (request.getStatus() != null) {
      builder.queryParam("status", request.getStatus());
    }

    if (request.getKeyword() != null) {
      builder.queryParam("keyword", request.getKeyword());
    }

    return builder.toUriString();
  }

  /**
   * 测试GB Gateway连接.
   *
   * @param baseUrl GB Gateway服务器地址
   * @return 是否连接成功
   */
  public static boolean testConnection(String baseUrl) {
    try {
      String url = baseUrl + "/api/health";

      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_JSON);

      HttpEntity<String> entity = new HttpEntity<>(headers);

      ResponseEntity<String> response = restTemplate.exchange(
          url,
          HttpMethod.GET,
          entity,
          String.class
      );

      return response.getStatusCode() == HttpStatus.OK;

    } catch (Exception e) {
      log.error("GB Gateway connection test failed", e);
      return false;
    }
  }
}
