# GEMINI.md

## Project Overview

This is a Java-based microservices project built with Maven. The project, named `skylink-server`, is designed as a backend platform for an industrial equipment control system. It utilizes the Spring Boot and Spring Cloud frameworks to create a modular and scalable architecture.

The project is divided into several modules:

*   `base-core`: Contains the core functionalities and data access objects (DAO).
*   `system-api`: The main API gateway that aggregates all the services.
*   `kvm-collectors`: A collection of modules for remote control services.
*   `system-monitor`: Provides monitoring capabilities for the system.
*   `system-protocol`: Handles system protocols.

The application is designed to be deployed using Docker, and it relies on several external services:

*   **MySQL:** As the primary database.
*   **Redis:** For caching and session management.
*   **RabbitMQ:** For message queuing.

The project uses Flyway for database schema migrations, ensuring that the database schema is always in sync with the application code.

## Building and Running

### Prerequisites

*   Java 21
*   Maven
*   Docker

### Building the Project

To build the project, run the following command from the root directory:

```bash
./mvnw clean install
```

### Running the Project

The project can be run using Docker Compose. From the `docker` directory, run the following command:

```bash
docker-compose up -d
```

This will start all the required services, including the main application, MySQL, Redis, and RabbitMQ.

## Development Conventions

### Coding Style

The project follows the Google Java Style Guide. The `checkstyle` plugin is configured to enforce this style.

### Testing

The project uses JUnit for unit testing. The tests are located in the `src/test/java` directory of each module. To run the tests, use the following command:

```bash
./mvnw test
```

### Database Migrations

Database migrations are managed using Flyway. The migration scripts are located in the `src/main/resources/db/migration` directory of the `system-api` module.

### Contribution Guidelines

While there are no explicit contribution guidelines, the project structure and build process suggest that new features should be developed in separate modules and then integrated into the `system-api` module.
